//
//  GenerateType.swift
//  PerformanceBezier
//
//  Created by tree on 2022/2/14.
//

import Foundation
import UIKit

public protocol ShapeGenerateType: AnyObject {
    
    var path: CGPath? { get }
    
    var trackingPoints: [CGPoint]? { get }
    
    func generator()
}

public typealias EffectArea = CGRect
public protocol PathGeneratorType: AnyObject {
    
    func updateRelativeView(_ view: UIView)
    
    func began(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?) -> EffectArea
    
    func push(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?) -> EffectArea
    
    func end(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?) -> EffectArea
    
    func clean()
    
    func pathRef() -> CGPath?
    
    func avaliablePoints() -> [ZDPointUnit]
    
    func pointRadius(_ point: ZDPointUnit) -> Optional<CGFloat>
    
}
