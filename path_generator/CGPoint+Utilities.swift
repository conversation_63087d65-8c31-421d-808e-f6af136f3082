import Foundation
import simd
import UIKit

public extension CGPoint{
    func translate(x: CGFloat, _ y: CGFloat) -> CGPoint {
        return CGPoint(x: self.x + x, y: self.y + y)
    }
    
    func translateX(x: CGFloat) -> CGPoint {
        return CGPoint(x: self.x + x, y: self.y)
    }
    
    func translateY(y: CGFloat) -> CGPoint {
        return CGPoint(x: self.x, y: self.y + y)
    }
    
    func invertY() -> CGPoint {
        return CGPoint(x: self.x, y: -self.y)
    }
    
    func xAxis() -> CGPoint {
        return CGPoint(x: 0, y: self.y)
    }
    
    func yAxis() -> CGPoint {
        return CGPoint(x: self.x, y: 0)
    }
    
    func addTo(a: CGPoint) -> CGPoint {
        return CGPoint(x: self.x + a.x, y: self.y + a.y)
    }
    
    func deltaTo(a: CGPoint) -> CGPoint {
        return CGPoint(x: self.x - a.x, y: self.y - a.y)
    }
    
    func multiplyBy(value:CGFloat) -> CGPoint{
        return CGPoint(x: self.x * value, y: self.y * value)
    }
    
    func length() -> CGFloat {
        return CGFloat(sqrt(CDouble(
            self.x*self.x + self.y*self.y
            )))
    }
    
    
    ///与该向量的垂直向量（在该向量的逆时针角度）
    var verticalVector: CGPoint {
        if !(x.isZero && y.isZero) {
            return CGPoint(x: -y, y: x)
        } else {
            return .zero
        }
    }
    
    /// 归一化向量
    var normalized: CGPoint {
        let quadrance = self.quadrance
        if quadrance > 0.0 {
            return self / sqrt(quadrance)
        } else {
            return .zero
        }
    }
    
    var quadrance: CGFloat {
        return x * x + y * y
    }

}


extension UIBezierPath {
    
    // 定义一个便利构造器，接收Catmull-Rom控制点数组和alpha值
    convenience init?(catmullRomPoints: [CGPoint], alpha: CGFloat) {
        // 调用自身的初始化方法
        self.init()
        
        // 如果控制点少于4个，无法形成Catmull-Rom样条曲线，返回nil
        if catmullRomPoints.count < 4 {
            return nil
        }
        
        // 定义起始和结束索引，用于迭代控制点
        let startIndex = 1
        let endIndex = catmullRomPoints.count - 2
        
        // 遍历控制点，从第2个点到倒数第二个点
        for i in startIndex ..< endIndex {
            // 计算前一个控制点，考虑循环情况
            let p0 = catmullRomPoints[i-1 < 0 ? catmullRomPoints.count - 1 : i - 1]
            // 当前控制点
            let p1 = catmullRomPoints[i]
            // 下一个控制点
            let p2 = catmullRomPoints[(i+1)%catmullRomPoints.count]
            // 下下一个控制点，考虑循环情况
            let p3 = catmullRomPoints[(i+2)%catmullRomPoints.count]
            
            // 计算控制点之间的距离
            let d1 = p1.deltaTo(a: p0).length()
            let d2 = p2.deltaTo(a: p1).length()
            let d3 = p3.deltaTo(a: p2).length()
            
            // 计算第一个控制点的切向量
            var b1 = p2.multiplyBy(value: pow(d1, 2 * alpha))
            b1 = b1.deltaTo(a: p0.multiplyBy(value: pow(d2, 2 * alpha)))
            b1 = b1.addTo(a: p1.multiplyBy(value:2.0 * pow(d1, 2.0 * alpha) + 3.0 * pow(d1, alpha) * pow(d2, alpha) + pow(d2, 2.0 * alpha)))
            b1 = b1.multiplyBy(value: 1.0 / (3 * pow(d1, alpha) * (pow(d1, alpha) + pow(d2, alpha))))
            
            // 计算第二个控制点的切向量
            var b2 = p1.multiplyBy(value: pow(d3, 2 * alpha))
            b2 = b2.deltaTo(a: p3.multiplyBy(value: pow(d2, 2 * alpha)))
            b2 = b2.addTo(a: p2.multiplyBy(value:2.0 * pow(d3, 2.0 * alpha) + 3.0 * pow(d3, alpha) * pow(d2, alpha) + pow(d2, 2.0 * alpha)))
            b2 = b2.multiplyBy(value: 1.0 / (3 * pow(d3, alpha) * (pow(d3, alpha) + pow(d2, alpha))))
            
            // 如果是第一个控制点，将路径移动到该点
            if i == startIndex {
                move(to: p1)
            }
            
            // 添加一个贝塞尔曲线段到路径，使用计算出的切向量作为控制点
            addCurve(to: p2, controlPoint1: b1, controlPoint2: b2)
        }
    }
}

extension CGPath {
    func forEach( body: @escaping @convention(block) (CGPathElement) -> Void) {
        typealias Body = @convention(block) (CGPathElement) -> Void
        let callback: @convention(c) (UnsafeMutableRawPointer, UnsafePointer<CGPathElement>) -> Void = { (info, element) in
            let body = unsafeBitCast(info, to: Body.self)
            body(element.pointee)
        }
        //print(MemoryLayout.size(ofValue: body))
        let unsafeBody = unsafeBitCast(body, to: UnsafeMutableRawPointer.self)
        self.apply(info: unsafeBody, function: unsafeBitCast(callback, to: CGPathApplierFunction.self))
    }
    func getPathElementsPoints() -> [CGPoint] {
        var arrayPoints : [CGPoint]! = [CGPoint]()
        self.forEach { element in
            switch (element.type) {
            case CGPathElementType.moveToPoint:
                arrayPoints.append(element.points[0])
            case .addLineToPoint:
                arrayPoints.append(element.points[0])
            case .addQuadCurveToPoint:
                arrayPoints.append(element.points[0])
                arrayPoints.append(element.points[1])
            case .addCurveToPoint:
                arrayPoints.append(element.points[0])
                arrayPoints.append(element.points[1])
                arrayPoints.append(element.points[2])
            default: break
            }
        }
        return arrayPoints
    }
    func getPathElementsPointsAndTypes() -> ([CGPoint],[CGPathElementType]) {
        var arrayPoints : [CGPoint]! = [CGPoint]()
        var arrayTypes : [CGPathElementType]! = [CGPathElementType]()
        self.forEach { element in
            switch (element.type) {
            case CGPathElementType.moveToPoint:
                arrayPoints.append(element.points[0])
                arrayTypes.append(element.type)
            case .addLineToPoint:
                arrayPoints.append(element.points[0])
                arrayTypes.append(element.type)
            case .addQuadCurveToPoint:
                arrayPoints.append(element.points[0])
                arrayPoints.append(element.points[1])
                arrayTypes.append(element.type)
                arrayTypes.append(element.type)
            case .addCurveToPoint:
                arrayPoints.append(element.points[0])
                arrayPoints.append(element.points[1])
                arrayPoints.append(element.points[2])
                arrayTypes.append(element.type)
                arrayTypes.append(element.type)
                arrayTypes.append(element.type)
            default: break
            }
        }
        return (arrayPoints,arrayTypes)
    }
}


public struct CatmullRomUnit {
    public let p0: CGPoint
    public let p1: CGPoint
    public let ctrl0: CGPoint
    public let ctrl1: CGPoint
}

public func caculateCatmullRom(_ points: [CGPoint], alpha: CGFloat) -> Optional<CatmullRomUnit> {
    if points.count != 4 {
        return nil
    }
    // 计算前一个控制点，考虑循环情况
    let p0 = points[0]
    // 当前控制点
    let p1 = points[1]
    
    // 下一个控制点
    let p2 = points[2]
    // 下下一个控制点，考虑循环情况
    let p3 = points[3]
    
    // 计算控制点之间的距离
    let d1 = p1.deltaTo(a: p0).length()
    let d2 = p2.deltaTo(a: p1).length()
    let d3 = p3.deltaTo(a: p2).length()
    
    // 计算第一个控制点的切向量
    var b1 = p2.multiplyBy(value: pow(d1, 2 * alpha))
    b1 = b1.deltaTo(a: p0.multiplyBy(value: pow(d2, 2 * alpha)))
    b1 = b1.addTo(a: p1.multiplyBy(value:2.0 * pow(d1, 2.0 * alpha) + 3.0 * pow(d1, alpha) * pow(d2, alpha) + pow(d2, 2.0 * alpha)))
    b1 = b1.multiplyBy(value: 1.0 / (3 * pow(d1, alpha) * (pow(d1, alpha) + pow(d2, alpha))))
    
    // 计算第二个控制点的切向量
    var b2 = p1.multiplyBy(value: pow(d3, 2 * alpha))
    b2 = b2.deltaTo(a: p3.multiplyBy(value: pow(d2, 2 * alpha)))
    b2 = b2.addTo(a: p2.multiplyBy(value:2.0 * pow(d3, 2.0 * alpha) + 3.0 * pow(d3, alpha) * pow(d2, alpha) + pow(d2, 2.0 * alpha)))
    b2 = b2.multiplyBy(value: 1.0 / (3 * pow(d3, alpha) * (pow(d3, alpha) + pow(d2, alpha))))
    
    return CatmullRomUnit(p0: p1, p1: p2, ctrl0: b1, ctrl1: b2)
}

class Simplify {
    static func getSqDist(p1: CGPoint, p2: CGPoint) -> CGFloat {
        let dx = p1.x - p2.x
        let dy = p1.y - p2.y
        return dx * dx + dy * dy
    }

    // square distance from a point to a segment
    static func getSqSegDist(p: CGPoint, p1: CGPoint, p2: CGPoint) -> CGFloat {
        var x = p1.x
        var y = p1.y
        let dx = p2.x - x
        let dy = p2.y - y

        if dx != 0 || dy != 0 {
            let t = ((p.x - x) * dx + (p.y - y) * dy) / (dx * dx + dy * dy)
            if t > 1 {
                x = p2.x
                y = p2.y
            } else if t > 0 {
                x += dx * t
                y += dy * t
            }
        }

        let dx2 = p.x - x
        let dy2 = p.y - y
        return dx2 * dx2 + dy2 * dy2
    }
    
    static func simplifyRadialDist(points: [CGPoint], sqTolerance: CGFloat) -> [CGPoint] {
        var prevPoint = points[0]
        var newPoints = [prevPoint]
        var point: CGPoint = .zero

        for i in 1..<points.count {
            point = points[i]
            if getSqDist(p1: point, p2: prevPoint) > sqTolerance {
                newPoints.append(point)
                prevPoint = point
            }
        }

        if prevPoint != point {
            newPoints.append(point)
        }

        return newPoints
    }

    static func simplifyDPStep(points: [CGPoint], first: Int, last: Int, sqTolerance: CGFloat, simplified: inout [CGPoint]) {
        var maxSqDist = sqTolerance
        var index: Int?

        for i in first + 1..<last {
            let sqDist = getSqSegDist(p: points[i], p1: points[first], p2: points[last])

            if sqDist > maxSqDist {
                index = i
                maxSqDist = sqDist
            }
        }

        if maxSqDist > sqTolerance {
            if let index = index, index - first > 1 {
                simplifyDPStep(points: points, first: first, last: index, sqTolerance: sqTolerance, simplified: &simplified)
            }
            if let index = index {
                simplified.append(points[index])
            }
            if let index = index, last - index > 1 {
                simplifyDPStep(points: points, first: index, last: last, sqTolerance: sqTolerance, simplified: &simplified)
            }
        }
    }
    
    static func simplifyDouglasPeucker(points: [CGPoint], sqTolerance: CGFloat) -> [CGPoint] {
        let last = points.count - 1
        var simplified = [points[0]]
        simplifyDPStep(points: points, first: 0, last: last, sqTolerance: sqTolerance, simplified: &simplified)
        simplified.append(points[last])
        return simplified
    }

    static func simplify(points: [CGPoint], tolerance: CGFloat, highestQuality: Bool) -> [CGPoint] {
        if points.count <= 2 {
            return points
        }

        let sqTolerance = tolerance * tolerance

        var simplifiedPoints = highestQuality ? points : simplifyRadialDist(points: points, sqTolerance: sqTolerance)
        simplifiedPoints = simplifyDouglasPeucker(points: simplifiedPoints, sqTolerance: sqTolerance)

        return simplifiedPoints
    }

    // export as AMD module / Node module / browser or worker variable
    // Note: Swift does not have direct equivalent for AMD or Node module exports
    
}
