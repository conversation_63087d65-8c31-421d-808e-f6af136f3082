//
//  KiloLineEnd.swift
//  
//
//  Created by WeIHa'S on 2022/5/10.
//

import Foundation
import simd
import CoreGraphics

///操作
public enum LineOperation: Int {
    ///不删不补
    case normal
    /// 只删不补
    case delete
    /// 不删但补
    case add
    /// 又删又补
    case exchange
    
    
    public init?(delete: Bool, add: Bool) {
        let rawValue = (delete ? 1 : 0) + (add ? 2 : 0)
        self.init(rawValue: rawValue)
    }
    
    var name: String {
        switch self {
        case .normal:
            return "normal"
        case .delete:
            return "delete"
        case .add:
            return "add"
        case .exchange:
            return "exchange"
        }
    }
}


public protocol LinePoint: Equatable {
    var point: CGPoint { get set }
    
    func supple(with p : Self) -> Self
}

extension LinePoint {
    static func - (lhs: Self, rhs: Self) -> vector_double2 {
        lhs.point - rhs.point
    }
    
    static func + (lhs: inout Self, rhs: vector_double2) {
        lhs.point = lhs.point + rhs
    }
    
    static func + (lhs: Self, rhs: vector_double2) -> Self {
        var a = lhs
        a.point = a.point + rhs
        return a
    }
    
    
}

/// 线头
///
/// 数据结构:
/// ```
/// 线头数据结构，是一个最多仅有三个元素的结构体: |X|A|B|
/// 每当压入新的P点，会自行计算Z的去留 : |X|A|B| <- P
/// 变成 |X|A|B|P|
/// 如果操作结果：normal -> |A|B|P|
/// 如果操作结果：delete -> |X|A|P|
/// 如果操作结果：add -> |B|M|P| M是ZP中点
/// 如果操作结果：exchange -> |A|M|P|
///
/// ```
/// 使用方法：
/// ```
/// var lineEnd = KiloLineEnd() // var lineEnd = KiloLineEnd.init(lim_a: 3, lim_b: 10, lim_c: 5)
/// //线头push结果
/// if lineEnd.push(point1) {
///     //保留前一个点
/// } else {
///     //抛弃前一个点
/// }
/// .......一直push线
///
/// ```
///
///
/// 参数意义
/// ```
///  //                C
///  //           b  / /
///  //            /  /
///  //         /    / a
///  //......A---->B
///  //         c
///  //
///
/// lim_a: 此参数的意义是BC的极限，只有当BC小于lim_a，也就是这个点和上一个点极近的时候才会触发增删操作，lim_a 越大，过滤的点越多
/// lim_b: 此参数的意义是AC的极限，只要AC的距离大于lim_b,就会触发平滑操作，去除B，而使用AC的中点B‘代替B
/// lim_c: 此参数的意义是AB的极限， 只要AB的距离大于lim_c, 一定会保留B点和C点，lim_c 越大，过滤的点越多
/// 建议 lim_b > lim_c > lim_a
/// ```
public struct KiloLineEnd<Element> where Element: LinePoint {
    //线头内存储的点
    private var points: [Element?] = [nil, nil, nil]
    //容量
    private let capacity: Int = 3
    
    ///
    public var lim_a: Double
    
    ///
    public var lim_b: Double
    
    /// 焦距
    public var lim_c: Double
    
    
    public init(lim_a: Double = 3.0, lim_b: Double = 8.0, lim_c: Double = 4.0) {
        self.lim_a = lim_a
        self.lim_b = lim_b
        self.lim_c = lim_c
    }
    
    /// 是否满了
    private var isFull: Bool {
        return points.count >= capacity
    }
}

//隐藏的操作
private extension KiloLineEnd {
    /// 判定操作
    /// - Parameter P: 新的点
    /// - Returns: Z点操作
    private func judgeOperation(with C: Element) -> LineOperation? {
        //                C
        //           b  / /
        //            /  /
        //         /    / a
        //......A---->B
        //         c
        
        guard let A = A,
              let B = B else { return .normal }
        
        
        let BC = C-B
        let AC = C-A
        let AB = B-A
        

        let dis_BC = BC.fastLength
        let dis_AC = AC.fastLength
        let dis_AB = AB.fastLength
        
        let cosb = (AB.lengthSqrt + BC.lengthSqrt - AC.lengthSqrt)/(2*dis_AB*dis_BC)
        //要删
        var delete = false
        //要补
        let add = false
        
        delete = (cosb <= -0.7)&&(dis_AB<c)
        
        if cosb == 0, dis_BC < a {
            delete = true
        }
        
        
//        if dis_AC > b {
//            delete = true
//            add = true
//        }
        
        let operation = LineOperation(delete: delete, add: add) ?? .normal
//        print("debug" ,"limc" ,lim_c ,"disBC", dis_BC, "a", a,"disAB", dis_AB, "c", c, "cos", cosb, operation.name  , separator: " ")
//        return .normal
        return operation
    }
    
    
    private var A: Element? {
        guard points.count >= 2 else { return nil }
        return points[points.endIndex - 2]
    }
    
    private var Pre_A: Element? {
        guard points.count >= 3 else { return nil }
        return points[points.endIndex - 3]
    }
    
    private var B: Element? {
        return points.last ?? nil
    }
    
    private var a: Double {
        return lim_a
    }
    
    private var b: Double {
        return lim_b
    }
    
    private var c: Double {
        return lim_c
    }
    
}


//开放的操作
public extension KiloLineEnd {
    /// 压元素进线头
    /// - Parameter point: 一个新的点
    /// - Returns: 是否应该删除判定点
    @discardableResult mutating func push(_ point: Element) -> LineOperation? {
       //如果该点和最后一个点相同，无操作
        if let last = self.points.last ,last?.point == point.point {
            return nil
        }
        
        guard let judgeState = judgeOperation(with: point) else { return nil }
        //判定操作
        switch judgeState {
        case .normal:
            break
        case .delete:
            points.removeLast()
        case .add:
            guard let last = points.last else { return .normal }
            points.append(last?.supple(with: point))
        case .exchange:
            //先删再补，先补再删，效果存疑
            //此处先补再删
            points.removeLast()
            if let last = points.last {
                points.append(last?.supple(with: point))
            }
            
        }
        
        points.append(point)
        points = points.suffix(capacity)
        return judgeState
    }

    
    /// 参数更新
    /// ```
    ///  //                C
    ///  //           b  / /
    ///  //            /  /
    ///  //         /    / a
    ///  //......A---->B
    ///  //         c
    ///  //
    ///  ```
    /// - Parameters:
    /// - Parameters:
    ///   - lim_a: 此参数的意义是BC的极限，只有当BC小于lim_a，也就是这个点和上一个点极近的时候才会触发增删操作，lim_a 越大，过滤的点越多
    ///   - lim_b: 此参数的意义是AC的极限，只要AC的距离大于lim_b,就会触发平滑操作，去除B，而使用AC的中点B‘代替B
    ///   - lim_c: 此参数的意义是AB的极限， 只要AB的距离大于lim_c, 一定会保留B点和C点，lim_c 越大，过滤的点越多
    ///   - scale: 缩放系数
    ///   建议 lim_b > lim_c > lim_a
    mutating func updateParameter(lim_a: Double = 3, lim_b: Double = 10, lim_c: Double = 5) {
        self.lim_a = lim_a
        self.lim_b = lim_b
        self.lim_c = lim_c
    }
    
    
    
    /// 倒数第一个
    var last: Element? {
        get {
            points[points.endIndex-1]
        }
        
        set {
            points[points.endIndex-1] = newValue
        }
    }
    
    
    /// 倒数第二个
    var lastTwo: Element? {
        get {
            points[points.endIndex-2]
        }
        set {
            points[points.endIndex-2] = newValue
        }
    }
    
    
    /// 倒数第三个
    var lastThree: Element? {
        get {
            points[points.endIndex-3]
        }
        
        set {
            points[points.endIndex-3] = newValue
        }
    }
}


extension CGPoint: LinePoint {

    
    public init(on point: CGPoint) {
        self = point
    }
    
    public var point: CGPoint {
        get {
            self
        }
        set {
            self = newValue
        }
    }
    
    public func supple(with p: CGPoint) -> CGPoint {
        return self.mid(to: p)
    }
}
