//
//  ZDPointUnit.swift
//  ZDDrawingKit
//
//  Created by tree on 2021/12/7.
//

import Foundation
import Accelerate
import UIKit

/* 点类型，
 origin代表此点是由用户触摸的点生成的，
 auxiliary代表是由我们增加的辅助点，
 drawable代表是我们增加的绘制顶点
 */
public enum ZDPointType: Int, Codable {
    case origin, auxiliary, drawable
}

public enum ZDTouchType: Int, Codable {
    case none,finger,pencil
    
    static func from_UITouchType(_ type: UITouch.TouchType) -> ZDTouchType {
        switch type {
        case .direct: return .finger
        case .pencil: return .pencil
        @unknown default: return .none
        }
    }
}

/// 绘制点
public struct ZDPointUnit: Codable {
    public var location = CGPoint.zero
    public var force: CGFloat = 1.0
    public var pointType: ZDPointType = .origin
    public var touchType: ZDTouchType = .none
    public var timeStamp:TimeInterval?
    public var altitude: CGFloat?
    public var azimuth: CGFloat?
    public var radius: CGFloat?
    
    // Pencil only.
    public var estimatedProperties: UITouch.Properties = []
    //预计有更新的属性
    public var estimatedPropertiesExpectingUpdates: UITouch.Properties = []
    
    var perpendicularForce: CGFloat {
        let force = force
        if let altitude = altitude {
            let result = force * CGFloat(sin(Double(altitude)))
            return result
        } else {
            return force
        }
    }

    public init(
        location: CGPoint,
        force: CGFloat = 1.0,
        pointType: ZDPointType = ZDPointType.origin,
        toucheType: ZDTouchType = ZDTouchType.none,
        timeStamp: TimeInterval? = Optional.none,
        altitude: CGFloat? = Optional.none,
        azimuth: CGFloat? = Optional.none,
        radius: CGFloat? = Optional.none
    ) {
        self.location = location
        self.force = force
        self.pointType = pointType
        self.touchType = toucheType
        self.timeStamp = timeStamp
        self.altitude = altitude
        self.azimuth = azimuth
        self.radius = radius
    }
    
    public init(touch: UITouch, inView: UIView) {
        self.init(location: touch.location(in: inView),
                  force: touch.force,
                  pointType: .origin,
                  toucheType: touch.type == .pencil ? .pencil : .finger,
                  timeStamp: touch.timestamp)
    }
    
    public init(point: CGPoint, touch: UITouch, azimuth: CGFloat? = Optional.none) {
        self.init(location: point,
                  force: touch.force,
                  pointType: .origin,
                  toucheType: touch.type == .pencil ? .pencil : .finger,
                  timeStamp: touch.timestamp,
                  altitude: touch.altitudeAngle,
                  azimuth: azimuth)
        
    }
    
    private func copy() -> ZDPointUnit {
        return ZDPointUnit(location: location, force: force, pointType: pointType, toucheType: touchType, timeStamp: timeStamp, altitude: altitude, azimuth: azimuth, radius: radius)
    }
    
    public func offset(_ locationOffset: CGPoint) -> ZDPointUnit {
        let newLocation = self.location.applying(.init(translationX: locationOffset.x, y: locationOffset.y))
        var newPoint = self.copy()
        newPoint.location = newLocation
        return newPoint
    }
    
    public func withRadius(_ radius: CGFloat) -> ZDPointUnit {
        var newPoint = self.copy()
        newPoint.radius = radius
        return newPoint
    }
    
    public func withLocation(_ location: CGPoint) -> ZDPointUnit {
        var newPoint = self.copy()
        newPoint.location = location
        return newPoint
    }
    
    public static func middle(p1: ZDPointUnit, p2: ZDPointUnit, pointType: ZDPointType = .auxiliary) -> ZDPointUnit {
        let location = CGPoint(x: (p1.location.x + p2.location.x) * 0.5, y: (p1.location.y + p2.location.y) * 0.5)
        var radius: Optional<CGFloat> = nil
        if let r1 = p1.radius, let r2 = p2.radius {
            radius = (r1 + r2) * 0.5
        }
        var altitude: Optional<CGFloat> = nil
        if let altitude1 = p1.altitude, let altitude2 = p2.altitude {
            altitude = (altitude1 + altitude2) * 0.5
        }
        var azimuth: Optional<CGFloat> = nil
        if let azimuth1 = p1.azimuth, let azimuth2 = p2.azimuth {
            azimuth = (azimuth1 + azimuth2) * 0.5
        }
        return ZDPointUnit(location: location,
                           force: (p1.force + p2.force) / 2.0,
                           pointType: pointType,
                           toucheType: p1.touchType,
                           altitude: altitude,
                           azimuth: azimuth,
                           radius: radius
        )
    }
    
    public static func distance(p1: ZDPointUnit, p2: ZDPointUnit) -> CGFloat {
        return p1.location.distanceTo(point: p2.location)
    }
    
    public static var zero: ZDPointUnit {
        return self.init(location: .zero)
    }
    
    public static func segmentPoints(source: ZDPointUnit, target: ZDPointUnit, count: Int) -> [ZDPointUnit] {
        let sourcePoint = source.location
        let targetPoint = target.location
        
        let offsetX = CGFloat(targetPoint.x - sourcePoint.x)
        let offsetY = CGFloat(targetPoint.y - sourcePoint.y)
        
        var segments: [ZDPointUnit] = []
        
        let stepX = offsetX / CGFloat(count)
        let stepY = offsetY / CGFloat(count)
        segments.append(source)
        for i in 1..<count {
            let x = sourcePoint.x + stepX * CGFloat(i)
            let y = sourcePoint.y + stepY * CGFloat(i)
            let point = CGPoint(x: x, y: y)
            let pointUnit = ZDPointUnit(location: point, force: source.force)
            segments.append(pointUnit)
        }
        segments.append(target)
        return segments
    }
    
    enum CodingKeys: CodingKey {
        case location
        case force
        case pointType
        case touchType
        case timeStamp
        case altitude
        case azimuth
        case radius
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.location = try container.decode(CGPoint.self, forKey: .location)
        self.force = try container.decode(CGFloat.self, forKey: .force)
        self.pointType = try container.decode(ZDPointType.self, forKey: .pointType)
        self.touchType = try container.decode(ZDTouchType.self, forKey: .touchType)
        self.timeStamp = try container.decodeIfPresent(TimeInterval.self, forKey: .timeStamp)
        self.altitude = try container.decodeIfPresent(CGFloat.self, forKey: .altitude)
        self.azimuth = try container.decodeIfPresent(CGFloat.self, forKey: .azimuth)
        self.radius = try container.decodeIfPresent(CGFloat.self, forKey: .radius)
    }
}

extension ZDPointUnit: Equatable {
    public static func == (lhs: Self, rhs: Self) -> Bool {
        var equal = lhs.location == rhs.location
        equal = equal || lhs.force == rhs.force
        return equal
    }
}

extension ZDPointUnit: Point2DRepresentable {
    public var xValue: Float {
        return self.location.xValue
    }
    
    public var yValue: Float {
        return self.location.yValue
    }
    
    public var cgPoint: CGPoint {
        return self.location
    }
}

// 点集滤波器
public struct ZDPointUnitFilter {
    // 移动平均滤波
    public static func movingAverageFilter(points: [ZDPointUnit], windowSize: Int) -> [ZDPointUnit] {
        
        let firstTouchType = points.first?.touchType ?? .none
        
        var filteredPoints: [ZDPointUnit] = []
        var windowPoints: [ZDPointUnit] = []
        let originPoints: [ZDPointUnit] = points
        
        for i in 0..<originPoints.count {
            let point = originPoints[i]
            windowPoints.append(point)
            if windowPoints.count > windowSize {
                windowPoints.removeFirst()
            }
            let averagePointX = windowPoints.reduce(0.0) { $0 + $1.location.x } / CGFloat(windowPoints.count)
            let averagePointY = windowPoints.reduce(0.0) { $0 + $1.location.y } / CGFloat(windowPoints.count)
            let times = windowPoints.compactMap(\.timeStamp)
            let averageT = times.reduce(0.0) { $0 + $1 } / Double(times.count)
            let averagePoint = CGPoint(x: averagePointX, y: averagePointY)
            let averageF = windowPoints.reduce(0.0) { $0 + $1.force } / CGFloat(windowPoints.count)
            filteredPoints.append(ZDPointUnit(location: averagePoint,
                                              force: averageF, 
                                              pointType: .auxiliary,
                                              toucheType: firstTouchType,
                                              timeStamp: averageT))
        }
        
        return filteredPoints
    }
    
    // 高斯滤波函数
    public static func gaussianFilter(data: [ZDPointUnit], sigma: Double, kernelSize: Int) -> [ZDPointUnit] {
        // 计算一维高斯核
        var kernel = [Double]()
        var sum = 0.0
        let a = 1 / sqrt(2 * Double.pi * sigma * sigma)
        for i in 0..<kernelSize {
            let x = Double(i) - Double(kernelSize) / 2
            let k = a * exp(-x * x / (2 * sigma * sigma))
            kernel.append(k)
            sum += k
        }
        // 归一化高斯核
        for i in 0..<kernelSize {
            kernel[i] /= sum
        }
        // 对 x 方向进行一维高斯滤波
        var tempX = [Double]()
        for i in 0..<data.count {
            var x = 0.0
            for k in 0..<kernelSize {
                var j = i + k - kernelSize / 2
                // 边界处理，这里采用镜像法
                if j < 0 {
                    j = -j
                }
                if j >= data.count {
                    j = 2 * data.count - j - 1
                }
                // 计算加权平均值
                x += data[j].location.x * kernel[k]
            }
            // 更新临时结果
            tempX.append(x)
        }
        // 对 y 方向进行一维高斯滤波
        var tempY = [Double]()
        for i in 0..<data.count {
            var y = 0.0
            for k in 0..<kernelSize {
                var j = i + k - kernelSize / 2
                // 边界处理，这里采用镜像法
                if j < 0 {
                    j = -j
                }
                if j >= data.count {
                    j = 2 * data.count - j - 1
                }
                // 计算加权平均值
                y += data[j].location.y * kernel[k]
            }
            // 更新临时结果
            tempY.append(y)
        }
        // 生成最终结果
        var result = [ZDPointUnit]()
        for i in 0..<data.count {
            let point = ZDPointUnit(location: CGPoint(x: tempX[i], y: tempY[i]),
                                    force: data[i].force,
                                    pointType: data[i].pointType,
                                    toucheType: data[i].touchType,
                                    timeStamp: data[i].timeStamp)
            result.append(point)
        }
        return result
    }
    
    // 距离卷积
    public static func samplingSteps(points: [ZDPointUnit], distance: CGFloat) -> [ZDPointUnit] {
        guard points.isEmpty == false else {
            return points
        }
        // 根据点与点之间的距离进行采样，如果累加距离超过了 distance ，则将中间的点都过滤掉，否则点都保留
        var filtedPoints: [ZDPointUnit] = []
        // 首点必定包含
        filtedPoints.append(contentsOf: points.prefix(1))
        
        var tmpDistanc = 0.0
        var prev: Optional<ZDPointUnit> = filtedPoints.first
        for point in points {
            // distance to prev
            if let p = prev {
                tmpDistanc += point.location.distanceTo(point: p.location)
            }
            if tmpDistanc >= distance {
                tmpDistanc = 0.0
                filtedPoints.append(point)
            }
            
            prev = point
        }
        // 尾点 替换为原来的尾点
        // 替换最后一点
        if let last = points.last {
            _ = filtedPoints.popLast()
            filtedPoints.append(last)
        }
        return filtedPoints
    }
    
    // 道格拉斯-普克算法
    public static func simplify(points: [ZDPointUnit], tolerance: CGFloat) -> [ZDPointUnit] {
        guard points.isEmpty == false else {
            return points
        }
        var filtedPoints: [ZDPointUnit] = []
        SwiftSimplify.simplify(points, tolerance: Float(tolerance)).forEach { point in
            filtedPoints.append(point)
        }
        return filtedPoints
    }
}

public final class KalmanFilter {
    /// 设置过程噪声 R
    public var processNoise: Double
    
    /// 测量噪声 Q
    public var measurementNoise: Double
    /// 状态向量 A
    private let stateVector: Double
    /// 控制向量 B
    private let controlVector: Double
    /// 测量向量 H
    private let measurementVector: Double
    /// 协方差 P
    private var covariance: Double? = nil
    /// 估计信号 X
    private var estimatedSignal: Double? = nil
    
    /// 创建一个一维卡尔曼滤波器
    ///
    /// - Parameters:
    ///   - processNoise: 过程噪声
    ///   - measurementNoise: 测量噪声
    ///   - stateVector: 状态向量
    ///   - controlVector: 控制向量
    ///   - measurementVector: 测量向量
    public init(processNoise: Double = 0.1, measurementNoise: Double = 1.0, stateVector: Double = 1.0, controlVector: Double = 0.0, measurementVector: Double = 1) {
        self.processNoise = processNoise
        self.measurementNoise = measurementNoise
        self.stateVector = stateVector
        self.controlVector = controlVector
        self.measurementVector = measurementVector
    }
    
    /// 过滤一个新值
    ///
    /// - Parameters:
    ///   - measurement: 测量值
    ///   - control: 控制值
    /// - Returns: 新的滤波值
    public func filter(measurement: Double, control: Int = 0) -> Double? {
        if (self.estimatedSignal == nil) {
            // 如果没有估计信号，则初始化估计信号和协方差
            self.estimatedSignal = (1 / self.measurementVector) * measurement
            self.covariance = (1 / self.measurementVector) * self.measurementNoise * (1 / self.measurementVector)
        } else if let predSignal = self.predict(control: control), let predCov = self.uncertainty {
            // 计算预测值
            
            // 卡尔曼增益
            let gain = predCov * self.measurementVector * (1 / ((self.measurementVector * predCov * self.measurementVector) + self.measurementNoise))
            
            // 修正
            self.estimatedSignal = predSignal + gain * (measurement - (self.measurementVector * predSignal))
            self.covariance = predCov - (gain * self.measurementVector * predCov)
        } else {
            assert(false)
        }
        return self.estimatedSignal
    }
    
    /// 预测下一个值
    ///
    /// - Parameter control: 控制值
    /// - Returns: 预测值
    public func predict(control: Int = 0) -> Double? {
        guard let estimation = self.estimatedSignal else {
            // 如果没有设置估计值，则返回 nil
            return nil
        }
        return (self.stateVector * estimation) + (self.controlVector * Double(control))
    }
    
    /// 返回滤波器的不确定性。如果没有设置协方差，则返回 nil。
    public var uncertainty: Double? {
        guard let cov = self.covariance else {
            return nil
        }
        return ((self.stateVector * cov) * self.stateVector) + self.processNoise
    }
    
    /// 最后一个滤波的测量值
    public var lastMeasurement: Double? {
        return self.estimatedSignal
    }
}
// 无迹卡尔曼一维滤波
open class UnscentedKalmanFilter {
    /// 过程噪声协方差矩阵
    public var processNoiseCovariance: Double
    
    /// 测量噪声协方差矩阵
    public var measurementNoiseCovariance: Double
    
    /// 状态向量的初始估计
    private var stateEstimate: Double
    
    /// 协方差矩阵的初始估计
    private var covarianceEstimate: Double
    
    /// Sigma点的缩放参数
    private let alpha: Double
    private let beta: Double
    private let kappa: Double
    
    /// 创建一个一维无迹卡尔曼滤波器
    /// - Parameters:
    ///  - processNoiseCovariance: 过程噪声协方差矩阵: 表示系统模型中的过程噪声的方差。过程噪声是由系统本身引入的不确定性，它表示了系统状态在没有控制输入的情况下的随机变化。较大的过程噪声会导致滤波器对系统变化更加敏感，从而使估计结果更加不稳定。
    ///  - measurementNoiseCovariance: 测量噪声协方差矩阵: 表示测量过程中的噪声的方差。测量噪声是由测量设备本身引入的不确定性，它表示了测量值与真实值之间的随机误差。较大的测量噪声会使滤波器对测量值的信任程度降低，从而减小测量值对估计结果的影响。
    ///  - initialEstimate: 状态向量的初始估计：表示系统当前的状态的估计值。它是滤波器对系统状态的最佳估计，通过不断更新和校正得到。
    ///  - initialCovariance: 协方差矩阵的初始估计：表示状态估计的不确定性。它是一个协方差矩阵，描述了状态估计的方差和协方差。较大的协方差表示滤波器对状态估计的不确定性较高，即滤波器对系统状态的估计不太可信。
    ///  - alpha: Sigma点的缩放参数：表示生成Sigma点时的缩放参数。它控制着Sigma点的分布范围，较大的alpha值会使Sigma点分布更广，从而更好地覆盖状态空间。通常情况下，alpha的取值范围是0到1之间。
    ///  - beta: Sigma点的缩放参数：表示生成Sigma点时的缩放参数。它对Sigma点的权重分配起着影响作用，较大的beta值会使Sigma点更加集中在均值附近。通常情况下，beta的取值为2。
    ///  - kappa: Sigma点的缩放参数：表示生成Sigma点时的缩放参数。它控制着Sigma点与均值之间的距离，较大的kappa值会使Sigma点更加分散。通常情况下，kappa的取值为0。
    public init(processNoiseCovariance: Double = 1.0, measurementNoiseCovariance: Double = 1.0, initialEstimate: Double = 0.0, initialCovariance: Double = 1.0, alpha: Double = 0.001, beta: Double = 2, kappa: Double = 0) {
        self.processNoiseCovariance = processNoiseCovariance
        self.measurementNoiseCovariance = measurementNoiseCovariance
        self.stateEstimate = initialEstimate
        self.covarianceEstimate = initialCovariance
        self.alpha = alpha
        self.beta = beta
        self.kappa = kappa
    }
    
    /// 生成Sigma点
    private func generateSigmaPoints(state: Double, covariance: Double) -> (Double, Double, Double) {
        let lambda = alpha * alpha * (1 + kappa) - 1
        let sigmaPoint1 = state
        let sigmaPoint2 = state + sqrt((1 + lambda) * covariance)
        let sigmaPoint3 = state - sqrt((1 + lambda) * covariance)
        return (sigmaPoint1, sigmaPoint2, sigmaPoint3)
    }
    
    /// 预测Sigma点
    private func predictSigmaPoints(sigmaPoints: (Double, Double, Double), control: Double) -> (Double, Double, Double) {
        // 这里需要根据你的系统模型来定义如何预测Sigma点
        // 以下是一个简单的线性预测
        let predictedSigmaPoint1 = sigmaPoints.0 + control
        let predictedSigmaPoint2 = sigmaPoints.1 + control
        let predictedSigmaPoint3 = sigmaPoints.2 + control
        return (predictedSigmaPoint1, predictedSigmaPoint2, predictedSigmaPoint3)
    }
    
    /// 根据预测的Sigma点更新状态估计和协方差估计
    private func updateEstimatesWithPredictedSigmaPoints(predictedSigmaPoints: (Double, Double, Double)) {
        let weights = calculateWeights()
        let mean = (weights.0 * predictedSigmaPoints.0) + (weights.1 * predictedSigmaPoints.1) + (weights.2 * predictedSigmaPoints.2)
        
        let variance = weights.0 * pow(predictedSigmaPoints.0 - mean, 2) +
                       weights.1 * pow(predictedSigmaPoints.1 - mean, 2) +
                       weights.2 * pow(predictedSigmaPoints.2 - mean, 2)
        
        stateEstimate = mean
        covarianceEstimate = variance + processNoiseCovariance
    }
    
    /// 计算Sigma点的权重
    private func calculateWeights() -> (Double, Double, Double) {
        let lambda = alpha * alpha * (1 + kappa) - 1
        let weight0 = lambda / (1 + lambda)
        let weight1 = 1 / (2 * (1 + lambda))
        let weight2 = weight1
        return (weight0, weight1, weight2)
    }
    
    /// 过滤一个新值
    public func filter(measurement: Double, control: Double = 0.0) -> Double {
        // 生成Sigma点
        let sigmaPoints = generateSigmaPoints(state: stateEstimate, covariance: covarianceEstimate)
        
        // 预测Sigma点
        let predictedSigmaPoints = predictSigmaPoints(sigmaPoints: sigmaPoints, control: control)
        
        // 更新状态估计和协方差估计
        updateEstimatesWithPredictedSigmaPoints(predictedSigmaPoints: predictedSigmaPoints)
        
        // 卡尔曼增益
        let kalmanGain = covarianceEstimate / (covarianceEstimate + measurementNoiseCovariance)
        
        // 更新最终的状态估计和协方差估计
        stateEstimate = stateEstimate + kalmanGain * (measurement - stateEstimate)
        covarianceEstimate = (1 - kalmanGain) * covarianceEstimate
        
        return stateEstimate
    }
    
    /// 最后一个滤波的测量值
    public var lastMeasurement: Double {
        return stateEstimate
    }
}
