//
//  ZDPathUtils.swift
//  ZDDrawingKit
//
//  Created by mian wang on 2022/6/9.
//

import Foundation

public struct ZDPathUtils {
    
    //连接两个点的路径，带有过渡点,圆心为传入点的位置
    public static func generateNormalPathWithTransitionPoint(pointA: CGPoint, pointB: CGPoint, transitionPoint: CGPoint, radiusA: CGFloat, radiusB: CGFloat) -> CGMutablePath {
        let subPath = CGMutablePath()
        if let unit = MathUtils.getPerfectControlPoint(centerA: pointA, centerB: pointB, transitionPoint: transitionPoint, radiusA: radiusA, radiusB: radiusB) {
            let (tangency1,
                 tangency2,
                 tangency3,
                 tangency4,
                 control1,
                 control2,
                 newT1Angel,
                 newT2Angel,
                 newT3Angel,
                 newT4Angel) = (
                    unit.tangencyAStartPointAndAngle.0,
                    unit.tangencyAEndPointAndAngle.0,
                    unit.tangencyBEndPointAndAngle.0,
                    unit.tangencyBStartPointAndAngle.0,
                    unit.controlPointAEndToBStart,
                    unit.controlPointBEndToAStart,
                    unit.tangencyAStartPointAndAngle.1,
                    unit.tangencyAEndPointAndAngle.1,
                    unit.tangencyBEndPointAndAngle.1,
                    unit.tangencyBStartPointAndAngle.1
                 )
            let path = generateAnticlockwisePath(pointA, pointB, tangency1, tangency2, tangency3, tangency4, radiusA, radiusB, newT1Angel, newT2Angel, newT3Angel, newT4Angel, control1, control2)
            subPath.addPath(path)
        }
        
        return subPath
    }
    
    //直接连接两个点的路径，不带过渡点
    public static func generateNormalPathWithOutTransitionPoint(pointA: CGPoint, pointB: CGPoint, radiusA: CGFloat, radiusB: CGFloat) -> CGMutablePath {
        let trasitionPoint = CGPoint.middle(p1: pointA, p2: pointB)
        let path = generateNormalPathWithTransitionPoint(pointA: pointA, pointB: pointB, transitionPoint: trasitionPoint, radiusA: radiusA, radiusB: radiusB)
        return path
    }
    
    //生成圆
    public static func generateCircle(center: CGPoint, radius: Double) -> CGMutablePath {
        let path = CGMutablePath()
        path.addEllipse(in: center.makeRect(width: radius*2, height: radius*2))
        return path
    }
    
    public static func generateArcCircle(center: CGPoint, radius: Double) -> CGMutablePath {
        let path = CGMutablePath()
        path.addArc(center: center, radius: radius, startAngle: 0, endAngle: Double.pi * 2, clockwise: true)
        return path
    }
    
    //生成逆时针闭合路径
    ///        ____  control1_____
    ///      A                                     C ------
    ///    /      \ --( angle1 )     (angle3 )--  \       \
    ///   |  centerA                              centerB   |
    ///    \     /--(angle2)         (angle4 )-- /        /
    ///      B                                      D ------
    ///       \___     control2 ____/
    public static func generateAnticlockwisePath(_ centerA:CGPoint, _ centerB:CGPoint, _ A: CGPoint, _ B: CGPoint, _ C: CGPoint, _ D: CGPoint, _ radiusA: CGFloat, _ radiusB: CGFloat,_ angel1: CGFloat, _ angel2: CGFloat,_ angel3: CGFloat, _ angel4: CGFloat,_ control1: CGPoint?, _ control2: CGPoint?) -> CGMutablePath {
        let subPath = CGMutablePath()
        subPath.move(to: A)
        subPath.addArc(center: centerA, radius: radiusA, startAngle: angel1, endAngle: angel2, clockwise: true)
        subPath.addLine(to: B)
        if let c2 = control2 {
            subPath.addQuadCurve(to: D, control: c2)
        } else {
            subPath.addLine(to: D)
        }
        subPath.addArc(center: centerB, radius: radiusB, startAngle: angel4, endAngle: angel3, clockwise: true)
        subPath.addLine(to: C)
        if let c1 = control1 {
            subPath.addQuadCurve(to: A, control: c1)
        } else {
            subPath.addLine(to: A)
        }
        subPath.closeSubpath()
        return subPath
    }
    
    
    public static func generateAnticlockwiseCurbicPath(_ centerA:CGPoint, _ centerB:CGPoint,radiusA: CGFloat, radiusB: CGFloat, upKeyPointA: CGPoint, upKeyPointB: CGPoint, downKeyPointA: CGPoint, downKeyPointB: CGPoint, upControlPointA: CGPoint, upControlPointB: CGPoint, downControlPointA: CGPoint, downControlPointB: CGPoint) -> CGMutablePath {
        let subPath = CGMutablePath()
        subPath.move(to: downKeyPointA)
        if downControlPointA.equalTo(downControlPointB) {
            subPath.addQuadCurve(to: downKeyPointB, control: downControlPointA)
        } else {
            subPath.addCurve(to: downKeyPointB, control1: downControlPointA, control2: downControlPointB)
        }
        
        subPath.addLine(to: upKeyPointB)
        if upControlPointA.equalTo(upControlPointB) {
            subPath.addQuadCurve(to: upKeyPointA, control: upControlPointA)
        } else {
            subPath.addCurve(to: upKeyPointA, control1: upControlPointB, control2: upControlPointA)
        }
        
        subPath.addLine(to: downKeyPointA)
        
        subPath.addPath(ZDPathUtils.generateCircle(center: centerA, radius: radiusA))
        subPath.addPath(ZDPathUtils.generateCircle(center: centerB, radius: radiusB))
        return subPath
    }
    
    public static func generateQuadraticBezierCurve(_ start: CGPoint,_ control: CGPoint, _ end: CGPoint) -> CGPath {
        let subPath = CGMutablePath()
        subPath.move(to: start)
        subPath.addQuadCurve(to: end, control: control)
        return subPath
    }
    
    public static func generateLinePath(_ start: CGPoint, _ end: CGPoint) -> CGPath {
        let subPath = CGMutablePath()
        subPath.move(to: start)
        subPath.addLine(to: end)
        return subPath
    }
    
    //根据两组外轮廓点生成闭合路径
    public static func generateNormalClosePath(_ upP1: CGPoint,_ upControl: CGPoint, _ upP2: CGPoint,_ downP1: CGPoint,_ downControl: CGPoint, _ downP2: CGPoint, center1: CGPoint, center2: CGPoint,radius1: CGFloat, radius2: CGFloat) -> CGPath {
        var subPath = CGMutablePath()
        subPath.move(to: upP1)
        subPath.addQuadCurve(to: upP2, control: upControl)
        MathUtils.addArcToPathWithTwoPointsOnCircle(center: center2, pointA: upP2, pointB: downP2, radius: radius2, isClockWise: false, path: &subPath)
        subPath.addQuadCurve(to: downP1, control: downControl)
        MathUtils.addArcToPathWithTwoPointsOnCircle(center: center1, pointA: downP1, pointB: upP1, radius: radius1, isClockWise: false, path: &subPath)
        subPath.closeSubpath()
        return subPath
    }
    
    //根据两组外轮廓点生成闭合路径
    public static func generateCircleClosePath(_ upP1: CGPoint,_ upControl: CGPoint, _ upP2: CGPoint,_ downP1: CGPoint,_ downControl: CGPoint, _ downP2: CGPoint,_ centerA: CGPoint, _ centerB: CGPoint, _ radiusA: CGFloat, _ radiusB: CGFloat, _ upLeftControl: CGPoint?, _ upRightControl: CGPoint?,_ downLeftControl: CGPoint?, _ downRightControl: CGPoint?) -> CGPath {
        let subPath = CGMutablePath()
        subPath.move(to: upP1)
        if let upLeftControl = upLeftControl, let upRightControl = upRightControl {
            subPath.addCurve(to: upP2, control1: upLeftControl, control2: upRightControl)
        } else {
            subPath.addQuadCurve(to: upP2, control: upControl)
        }
        
        subPath.addLine(to: downP2)
        if let downLeftControl = downLeftControl, let downRightControl = downRightControl {
            subPath.addCurve(to: downP1, control1: downLeftControl, control2: downRightControl)
        } else {
            subPath.addQuadCurve(to: downP1, control: downControl)
        }
        
        subPath.addLine(to: upP1)
        subPath.closeSubpath()
        
//        subPath.addEllipse(in: CGRect(x: centerA.x - radiusA, y: centerA.y - radiusA, width: radiusA*2, height: radiusA*2))
//        subPath.addEllipse(in: CGRect(x: centerB.x - radiusB, y: centerB.y - radiusB, width: radiusB*2, height: radiusB*2))
        
        subPath.addPath(generateCircle(center: centerA, radius: radiusA))
        subPath.addPath(generateCircle(center: centerB, radius: radiusB))
        
        return subPath
    }
      
    
}
