//
//  FittingPathGenerator.swift
//  ZDDrawingKit
//
//  Created by tree on 2022/2/16.
//

import Foundation
import UIKit
import simd

// 通过类来封装一个结构体，保持一个指向结构体的指针，这样就可以保持结构体的引用
// 在更新点信息的时候，不必考虑结构体的定位问题
internal class PointUnitRef {
    // 表示的是录入的原始点
    private(set) var origin: ZDPointUnit
    private(set) var point: ZDPointUnit
    // 这里的flag表示的是: 是否通过Pencil接口更新过点的信息(压力信息)
    private(set) var hasUpdated: Bool = false
    
    init(point: ZDPointUnit, hasUpdated: Bool = false) {
        self.origin = point
        self.point = point
    }
    
    func update(point: ZDPointUnit, update: Bool = false) {
        self.point = point
        self.hasUpdated = update
    }
    
    func copy() -> PointUnitRef {
        let ref = PointUnitRef.init(point: origin, hasUpdated: hasUpdated)
        ref.update(point: self.point, update: self.hasUpdated)
        return ref
    }
}

// 锥度配置
public struct TaperConfig {
    // 起点锥度配置 (范围是 0-1）半径逐步变大
    let head: Optional<CGFloat>
    // 终点锥度配置 (范围是 0-1）
    let tail: Optional<CGFloat>
    
    let maxHeadCount: Int
    
    let maxTailCount: Int
    
    public init(head: Optional<CGFloat>, tail: Optional<CGFloat>, maxHeadCount: Int = 4, maxTailCount: Int = 6) {
        self.head = head
        self.tail = tail
        self.maxHeadCount = maxHeadCount
        self.maxTailCount = maxTailCount
//        print("tapper count:",maxHeadCount,maxTailCount)
    }
    
    var effectHeadCount: Optional<Int> {
        guard let head = head else { return nil }
        return Int(CGFloat(maxHeadCount) * head)
    }
    
    var effectTailCount: Optional<Int> {
        guard let tail = tail else { return nil }
        return Int(CGFloat(maxTailCount) * tail)
    }
}

// 表示内部的状态 Flags
fileprivate class TaperState {
    
    var taperHeadMax: Optional<CGFloat> = nil
    
    var taperHeadDone: Bool = false
    
    var taperTailDone: Bool = false
}

/// 注意📢📢📢📢
/// 此文件内容慎重改动
/// 压力压感线算法
final public class ForceFittingPathGenerator_v2: PathGeneratorType {
    
    
    public struct Params {
        
        var radius: CGFloat
        //最小压力系数
        public let calligraphyMinForce: CGFloat
        //最大压力系数
        public let calligraphyMaxForce: CGFloat
        //压力灵敏度
        public let calligraphyForceSensitiveValue: CGFloat
        //书写压力， 需要使用多大的压力才能改变曲线的变化，默认值是0, 范围【0,1.0】
        public let calligraphyWrittenForce: CGFloat
        //宽度平滑，默认值是0.4 范围【0,1.0】
        public let calligraphyWidthSmooth: CGFloat
        //运动平滑， 等于0就不会使用polishtool过滤点.范围【0,1.0】
        public let calligraphyMotionSmooth: CGFloat
        //最小宽度,百分比
        public var calligraphyMinLineWidth: CGFloat = 0.1
        //使用高度角
        public var calligraphyUseAltitude: Bool = true
//        //使用方位角
//        public var useAzimuth: Bool = true
//        //偏好方位角
//        public var preferredAzimuth: CGFloat = .pi/4
        
        
        // 压力锥度
        public let taper: TaperConfig
        
        public init(radius: CGFloat,
             calligraphyMinForce: CGFloat = 0.1,
             calligraphyMaxForce: CGFloat = 1.5,
             calligraphyForceSensitiveValue: CGFloat = 0.75,
             calligraphyWrittenForce: CGFloat = 0.5,
             calligraphyWidthSmooth: CGFloat = 0.5,
             calligraphyMotionSmooth: CGFloat = 1.0,
             calligraphyMinLineWidth: CGFloat = 0.1,
             taper: TaperConfig = .init(head: .none, tail: .none),
             calligraphyUseAltitude:Bool = false) {
            self.radius = radius
            self.calligraphyMinForce = calligraphyMinForce
            self.calligraphyMaxForce = calligraphyMaxForce
            self.calligraphyForceSensitiveValue = calligraphyForceSensitiveValue
            self.calligraphyWrittenForce = calligraphyWrittenForce
            self.calligraphyWidthSmooth = calligraphyWidthSmooth
            self.calligraphyMotionSmooth = calligraphyMotionSmooth
            self.calligraphyMinLineWidth = calligraphyMinLineWidth
            self.taper = taper
            self.calligraphyUseAltitude = calligraphyUseAltitude
        }
    }
    
    // 是否在第一个点约束的很小范围内，如果是，则扩大第一个起始点构造的圆 的半径,第二个点并不需要参与轨迹的生成
    @available(*, deprecated, message: "暂时没有用到")
    private var isInFirstPointDomain: Bool = true
    
    //记录画布缩放比例
    private var scale: CGFloat = 1.0
    
    // 通过算法来过滤一些不必要的点，例如画的很直，那么路径中的一些点是可以省略的 可以减少点的数量
    private var pathPolishTool: KiloLineEnd = KiloLineEnd<CGPoint>(lim_a: 3, lim_b: 8, lim_c: 4)
    
    // 配置参数，只读
    private let params: Params
    
    private var taperState: TaperState = .init()
    
    var minRadius: CGFloat {(params.calligraphyMinLineWidth + 0.01) * params.radius}
    var maxRadius: CGFloat { generateRadiusWithForce(force: params.calligraphyMaxForce) }
    
    // 缓存点池容量
    private var cacheSize: Int = 4
    
    private var trackedTouch: UITouch?
    // 判断是否是 pencil 模式
    private var isPencil: Bool {
        trackedTouch?.type == .pencil
    }
    
    // 触发更新点信息后，需要通知外界刷新对应的区域
    public var updateEstimateBlock: ((EffectArea) -> ())?

    // 记录需要更新的点，如果更新过了，则在这里移除
    private var outstandingUpdateIndexes = [Int: PointUnitRef]()
        
    // 关联的视图
    private weak var view: Optional<UIView> = nil
    
    // 缓存点集合
    internal private(set) var cachingPoints: [PointUnitRef] = []
    internal private(set) var confirmedPoints: [PointUnitRef] = []
    // 对应调整过尾部锥度的缓存点信息，用来处理 绘制的 最后一点 到 采样点之间的路径。表现的跟手一些
    internal private(set) var cachingPointsWithTaper: [ZDPointUnit] = []
    // 已经确定下来的路径 【不会修改的】
    internal private(set) var confirmPath: CGMutablePath = CGMutablePath()
    // 缓存的路径
    internal private(set) var cachePath: CGMutablePath = CGMutablePath()
    // 用来渲染 绘制的最后一点到 采样点之间的路径
    internal private(set) var samplingPath: CGMutablePath = CGMutablePath()
    // 用于缓存 提交点的 渲染上下文
    // 渲染临时点的时候不会更新这个信息
    // 也不会存储
    internal private(set) var renderContext: Optional<RenderContext> = .none
    //压力模式
    private var isForceMode = true
    
    public init(params: Params, scaleFactor: CGFloat, isForceMode: Bool) {
        self.params = params
        self.scale = scaleFactor
        let effectHead = params.taper.effectHeadCount ?? 0
        let effectTail = params.taper.effectTailCount ?? 0
        self.cacheSize = max(effectHead, effectTail) + 1
        self.isForceMode = isForceMode
    }
    
    public func generateRadiusWithForce(force: CGFloat, altitude: Optional<CGFloat> = nil, azimuth: Optional<CGFloat> = nil) -> CGFloat {
        let forceMax = params.calligraphyMaxForce * 1.5
        let forceSensitive = max(params.calligraphyForceSensitiveValue, 0.1)
        var f = force * 1.3 + 0.5 * (1.0 - forceSensitive) + 0.15
        //使用执笔高度角
        if let altitude = altitude, params.calligraphyUseAltitude {
            f = f * CGFloat(sin(Double(altitude)))
        }
        f = max(params.calligraphyMinForce, min(f, forceMax))
        let radius = (f*0.5 + f*(1-params.calligraphyWrittenForce)) * params.radius
        return radius
    }
    
    // 更新点坐标信息，做一些预处理
    // 包含将 压力转换为半径
    // 如果是 Pencil 的事件，则需要订阅其更新事件[可能]
    func updatePoint(point: ZDPointUnit, touches: Set<UITouch>?, event: UIEvent?, view: UIView?) -> ZDPointUnit {
        var newPoint: ZDPointUnit = point
        if let view = view,
           let touch = getAppendTouches(touches: touches, event: event) {
            let location = newPoint.location
            newPoint = ZDPointUnit(point: location, touch: touch)
            //使用速度转压力
            if let lastPoint = self.cachingPoints.last, self.isForceMode == false {
                let radius = speedToRadius(pointA: lastPoint.point, pointB: newPoint)
                newPoint = newPoint.withRadius(radius)
            } else if let radius = pointRadius(point) {
                //使用系统自带压力
                newPoint = newPoint.withRadius(radius)
                newPoint.estimatedProperties = touch.estimatedProperties
                newPoint.estimatedPropertiesExpectingUpdates = touch.estimatedPropertiesExpectingUpdates
                newPoint.altitude = touch.altitudeAngle
                newPoint.azimuth = touch.azimuthAngle(in: view)
            }
        }
        
        if newPoint.radius == nil,
           let radius = pointRadius(newPoint) {
            newPoint = newPoint.withRadius(radius)
        }
        
        return newPoint
    }
    
    public func began(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil) -> EffectArea {
        self.clean()
        guard trackedTouch == nil else {return .zero}
        trackedTouch = touches?.first

        let filterPointsFactor: CGFloat = 0.1 + params.calligraphyMotionSmooth * 0.5
        pathPolishTool.updateParameter(lim_a: 0.6/scale, lim_c: (params.radius * 2.0)*filterPointsFactor)
        return append(point: point, touches: touches, event: event, view: view)
    }
        
    public func push(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil) -> EffectArea {
        let effectArea = append(point: point, touches: touches, event: event, view: view)
        return effectArea
    }
    
    public func end(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil) -> EffectArea {
        let effectArea = append(point: point, touches: touches, event: event, view: view, isEnd: true)
        return effectArea
    }
    
    public func updateRelativeView(_ view: UIView) {
        self.view = view
    }
    
    public func pointRadius(_ point: ZDPointUnit) -> Optional<CGFloat> {
        generateRadiusWithForce(force: point.force, altitude: point.altitude)
    }
    
    func speedToRadius(pointA: ZDPointUnit, pointB : ZDPointUnit) -> CGFloat {
        guard let timeStamp1 = pointA.timeStamp,
              let timeStamp2 = pointB.timeStamp  else { return 0 }
        let interval = timeStamp2 - timeStamp1
        let speed = MathUtils.getSpeedBetweenTwoPointsWithTimeInterval(pointA: pointA.location, pointB: pointB.location, interval: interval)
        let preSpeedForce = MathUtils.newBrushTransformSpeedToForce(speed, params.calligraphyMaxForce, params.calligraphyMinForce, scale, fasterIsThick: true)
        let radius = generateRadiusWithForce(force: preSpeedForce)
//        print("speed:",speed,preSpeedForce,radius)
        return radius
    }
    
    func getAppendTouches(touches: Set<UITouch>?, event: UIEvent?) -> Optional<UITouch> {
        guard let touchToAppend = trackedTouch, touches?.contains(touchToAppend) == true
        else { return nil }
        
        if let event = event {
            guard let coalescedTouches = event.coalescedTouches(for: touchToAppend) else {return nil}
            let lastIndex = coalescedTouches.count - 1
            let lastTouch = coalescedTouches[lastIndex]
            return lastTouch
        }
        return trackedTouch
    }
    
    func handleTailedPointUpdate(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil, view: UIView? = nil) -> EffectArea {
        // 仅用来更新 临时路径
        // 用来处理跟手的显示效果
        // 最后两个点
        guard cachingPointsWithTaper.isEmpty == false
        else { return .zero }
        
        let trimPoints = (self.confirmedPoints.suffix(2).map(\.point) + cachingPointsWithTaper).suffix(2)
        let lastCenter: Optional<ZDPointUnit>
        if trimPoints.count == 1 {
            lastCenter = trimPoints.last
        } else if trimPoints.count == 2 {
            let ps = Array(trimPoints)
            let p = ps[0]
            let c = ps[1]
            lastCenter = ZDPointUnit.middle(p1: p, p2: c)
        } else {
            lastCenter = cachingPointsWithTaper.last
        }
        guard let cachingLast = lastCenter else {
            return .zero
        }
        
        let radius = cachingLast.radius ?? self.minRadius
        let currentPoint = point.withRadius(radius)
        let path = handleTwoWhenEnd(circleA: cachingLast, circleB: currentPoint)
        samplingPath = CGMutablePath()
        samplingPath.addPath(path)
        let boxes: [CGRect] = [self.samplingPath.boundingBox]
        return rectsBoundingBox(rects: boxes)
    }
    
    //现在是每个触摸对应多个点，之前是一个触摸对应一个点
    func append(point: ZDPointUnit, touches: Set<UITouch>? = nil, event: UIEvent? = nil, view: UIView? = nil, isEnd: Bool = false) -> EffectArea {
        // 这里确保将点坐标的压力信息转换到圆的半径，后面都使用 半径信息，屏蔽压力
        let point: ZDPointUnit = updatePoint(point: point, touches: touches, event: event, view: view)
        assert(point.radius != nil)
                
        let ref = PointUnitRef(point: point)
        
        
        guard let newRef = shouldPeddingToCache(ref: ref, isEnd: isEnd) else {
            return handleTailedPointUpdate(point: point, touches: touches, event: event, view: view)
        }
        self.samplingPath = CGMutablePath()
        let finalConfirmedPoints = self.confirmedPoints
        var finalRemainPoints = self.cachingPoints
        finalRemainPoints.append(newRef)
        
        // 添加到缓存点集合中，判断是否需要产生确定的点集合， 如果需要产生，返回值就是 确定的点 、 剩余的缓存点集合
        // 缓存点 是有容量的，当缓存点的容量达到上限后，就会将头部的点推出, 也就是确定了一个点
        var (penddingPoints, remainPoints) = confirmPointInCaching(cachingPoints: finalRemainPoints, confirmedPoints: finalConfirmedPoints, params: params, isEnd: isEnd)
        // 确定的点是不会改变的，但是缓存点为了显示效果，有可能会多次调整
        // 这里使用一份拷贝的数据，渲染的数据和缓存的数据分离开，避免多次修改缓存值
        finalRemainPoints = remainPoints.map({ $0.copy() })
        
        // 产生了确定的点集合后，将这些点进行一些处理，例如 压力锥度，压力平滑，流线平滑等
        (penddingPoints, remainPoints) = mappingPeddingPointsInTaper(penddingPoints: penddingPoints, confirmPoints: finalConfirmedPoints, remainPoints: remainPoints, taperState: self.taperState, isEnd: isEnd)
        
        let (penddingPath, finalConfirmed) = renderConfirmedPath(confirmed: finalConfirmedPoints, pendding: penddingPoints, remained: remainPoints)

        // 绘制临时点集合
        let finalCachedPath = CGMutablePath()
        if let path = updateCachingPath(confirmPoints: finalConfirmed, cachedPoints: remainPoints) {
            finalCachedPath.addPath(path)
        }
        
        self.confirmedPoints = finalConfirmed
        if let penddingPath = penddingPath {
            self.confirmPath.addPath(penddingPath)
        }
        self.cachingPoints = finalRemainPoints
        self.cachePath = finalCachedPath
        var effectRects: [CGRect] = []
        if let penddingPath = penddingPath {
            effectRects.append(penddingPath.boundingBox)
        }
        
        cachingPointsWithTaper = remainPoints.map(\.point)
        if isEnd == false {
            // 跟手处理
            let tailedEffect = handleTailedPointUpdate(point: point, touches: touches, event: event, view: view)
            effectRects.append(tailedEffect)
        }
        effectRects.append(finalCachedPath.boundingBox)
        return rectsBoundingBox(rects: effectRects)
    }
    
    public func clean() {
        trackedTouch = nil
        self.confirmPath = CGMutablePath()
        self.cachePath = CGMutablePath()
        self.samplingPath = CGMutablePath()
        self.confirmedPoints = []
        self.cachingPoints = []
        self.cachingPointsWithTaper = []
        self.renderContext = .none
    }
    
    public func pathRef() -> CGPath? {
        let path = CGMutablePath()
        path.addPath(self.confirmPath)
        path.addPath(self.cachePath)
        path.addPath(self.samplingPath)
        return path
    }
    
    public func avaliablePoints() -> [ZDPointUnit] {
        return (self.confirmedPoints + self.cachingPoints).map { $0.point }
    }
}


//MARK: -路径生成法-
extension ForceFittingPathGenerator_v2  {
    
    //处理结束时只有二个采样点点的情况
    func handleTwoWhenEnd(circleA: ZDPointUnit, circleB: ZDPointUnit) -> CGPath {
        let center1 = circleA.location
        let center2 = circleB.location
        let radius1 = circleA.radius ?? self.minRadius
        let radius2 = circleB.radius ?? self.minRadius
        
        if let u1 = MathUtils.getFourContactPointsOfTwoCircle(centerA: center1, centerB: center2, radiusA: radius1, radiusB: radius2) {
            let currentUpKeyA = u1.tangencyAStartPointAndAngle.0
            let currentDownKeyA = u1.tangencyAEndPointAndAngle.0
            let currentUpKeyB = u1.tangencyBEndPointAndAngle.0
            let currentDownKeyB = u1.tangencyBStartPointAndAngle.0
            let upControl = currentUpKeyA.mid(to: currentUpKeyB)
            let downControl = currentDownKeyA.mid(to: currentDownKeyB)
            let path = ZDPathUtils.generateNormalClosePath(currentUpKeyA, upControl, currentUpKeyB, currentDownKeyA, downControl, currentDownKeyB, center1: center1, center2: center2, radius1: radius1, radius2: radius2)
            return path
        } else {
            let radius = max(radius1, radius2)
            let mid = center1.mid(to: center2)
            let path = ZDPathUtils.generateCircle(center: mid, radius: radius)
            return path
        }
    }
    
    //处理结束时只有三个采样点的情况
    func handleThreeWhenEnd(circleA: ZDPointUnit, circleB: ZDPointUnit) -> CGPath {
        let center1 = circleA.location
        let center2 = circleB.location
        let radius1 = circleA.radius ?? self.minRadius
        let radius2 = circleB.radius ?? self.minRadius
        
        if let unit1 = MathUtils.getFourContactPointsOfTwoCircle(centerA: center1, centerB: center2, radiusA: radius1, radiusB: radius2) {
            let upKeyPointA = unit1.tangencyAStartPointAndAngle.0
            let downKeyPointA = unit1.tangencyAEndPointAndAngle.0
            let upKeyPointB = unit1.tangencyBEndPointAndAngle.0
            let downKeyPointB = unit1.tangencyBStartPointAndAngle.0
            
            var path = CGMutablePath()
            path.move(to: upKeyPointA)
            path.addLine(to: upKeyPointB)
            MathUtils.addArcToPathWithTwoPointsOnCircle(center: center2, pointA: upKeyPointB, pointB: downKeyPointB, radius: radius2, isClockWise: false, path: &path)
            path.addLine(to: downKeyPointA)
            MathUtils.addArcToPathWithTwoPointsOnCircle(center: center1, pointA: downKeyPointA, pointB: upKeyPointA, radius: radius1, isClockWise: false, path: &path)
            path.closeSubpath()
            return path
        } else {
            let radius = max(radius1, radius2)
            let mid = center1.mid(to: center2)
            let path = ZDPathUtils.generateCircle(center: mid, radius: radius)
            return path
        }
    }
    
    func connectCircles(circleA: ZDPointUnit, transition: ZDPointUnit, circleB: ZDPointUnit, nextPoint: ZDPointUnit? = Optional.none, renderContext: RenderContext? = Optional.none) -> (CGPath, RenderContext?) {
        let center1 = circleA.location
        let center2 = circleB.location
        let radius1 = circleA.radius ?? self.minRadius
        let radius2 = circleB.radius ?? self.minRadius
//        print("speed real radius:",radius1,radius2)
        let d = center1.distanceTo(point: center2)
        
        let vec_cenA_Trasition = (transition.location - circleA.location)
        let vec_cenB_Trasition = (transition.location - circleB.location)
        let vec_cenA_Trasition_normal = vec_cenA_Trasition.normalized
        let vec_cenB_Trasition_normal = vec_cenB_Trasition.normalized

        //与圆的交点向量
        let verticalA = vec_cenA_Trasition_normal.verticalVector.multiplyBy(value: radius1)
        let verticalB = vec_cenB_Trasition_normal.verticalVector.multiplyBy(value: radius2)
        
        //控制变量1，调整落脚点的位置
        var upKeyPointA = center1 - verticalA
        var downKeyPointA = center1 + verticalA
        var upKeyPointB = center2 + verticalB
        var downKeyPointB = center2 - verticalB
        
        //控制变量2，调整手柄的长度
        var upHandleLenA = radius1
        var upHandleLenB = radius2
        var downHandleLenA = radius1
        var downHandleLenB = radius2
        
        var upControlA = upKeyPointA + vec_cenA_Trasition_normal*radius1
        var downControlA = downKeyPointA + vec_cenA_Trasition_normal*radius1
        var upControlB = upKeyPointB + vec_cenB_Trasition_normal*radius2
        var downControlB = downKeyPointB + vec_cenB_Trasition_normal*radius2
        
        //寻找落脚点与控制点
        upControlA = upKeyPointA.mid(to: upKeyPointB)
        upControlB = upControlA
        downControlA = downKeyPointA.mid(to: downKeyPointB)
        downControlB = downControlA
        
        let cachedKeyPoint = renderContext?.keyPoint
        var lastUpKeyPointA: Optional<CGPoint> = nil
        var lastDownKeyPointA: Optional<CGPoint> = nil
        
        let ratio = 0.333

        //寻找B落脚点,落脚点更改后，控制点需要重新计算
        if let nextPoint = nextPoint {
            let center3 = nextPoint.location
            let radius3 = nextPoint.radius ?? self.minRadius
            
            //求中间圆与左、右圆的两公切点之间的中点
            let u1 = MathUtils.getFourContactPointsOfTwoCircle(centerA: center1, centerB: center2, radiusA: radius1, radiusB: radius2)
            let u2 = MathUtils.getFourContactPointsOfTwoCircle(centerA: center2, centerB: center3, radiusA: radius2, radiusB: radius3)
            
//          //如果两个圆是包含的关系处理，异常情况处理
            let unit1Contains: Bool = u1 == nil
            let unit2Contains: Bool = u2 == nil
            if unit1Contains {
                //如果前面的单元是包含关系
                let newContext = renderContext?.withKeyPoint(newKeyPoint: CacheKeyPoint(lastUpKeyPointA: nil, lastDownKeyPointA:  nil)).withIsStart(isStart: false).withIsEnd(isEnd: false)
//                print("next u1 出现圆包含")
                let path = ZDPathUtils.generateCircle(center: center2, radius: radius2)
                return (path,newContext)
            } else if unit1Contains == false && unit2Contains {
                //如果仅后面的单元是包含关系，直接采用直连的方式
                if let unit1 = u1 {
                    let currentUpKeyB = unit1.tangencyBEndPointAndAngle.0
                    let currentDownKeyB = unit1.tangencyBStartPointAndAngle.0
                    
                    upKeyPointA = unit1.tangencyAStartPointAndAngle.0
                    downKeyPointA = unit1.tangencyAEndPointAndAngle.0
                    upKeyPointB = currentUpKeyB
                    downKeyPointB = currentDownKeyB
                    
                    let newContext = renderContext?.withKeyPoint(newKeyPoint: CacheKeyPoint(lastUpKeyPointA: upKeyPointB, lastDownKeyPointA:  downKeyPointB)).withIsStart(isStart: false).withIsEnd(isEnd: false)
//                    print("next u2 出现圆包含")
                    
                    var path = CGMutablePath()
                    path.move(to: upKeyPointA)
                    path.addLine(to: upKeyPointB)
                    MathUtils.addArcToPathWithTwoPointsOnCircle(center: center2, pointA: upKeyPointB, pointB: downKeyPointB, radius: radius2, isClockWise: false, path: &path)
                    path.addLine(to: downKeyPointA)
                    MathUtils.addArcToPathWithTwoPointsOnCircle(center: center1, pointA: downKeyPointA, pointB: upKeyPointA, radius: radius1, isClockWise: false, path: &path)
                    path.closeSubpath()
                    return (path,newContext)
                }
            }
            
            //处理两两之间都有公切点的情况，普通情况处理
            guard let unit1 = u1, let unit2 = u2 else { return
                (ZDPathUtils.generateCircle(center: center2, radius: radius2),renderContext)
            }

            let currentUpKeyB = unit1.tangencyBEndPointAndAngle.0
            let currentDownKeyB = unit1.tangencyBStartPointAndAngle.0
            let nextUpKeyA = unit2.tangencyAStartPointAndAngle.0
            let nextDownKeyA = unit2.tangencyAEndPointAndAngle.0
            
            upKeyPointA = cachedKeyPoint?.lastUpKeyPointA ?? upKeyPointA
            downKeyPointA = cachedKeyPoint?.lastDownKeyPointA ?? downKeyPointA
            //起始点没有上个点作参考,所以要调整关键点的位置
            if renderContext?.isStart == true {
                upKeyPointA = unit1.tangencyAStartPointAndAngle.0
                downKeyPointA = unit1.tangencyAEndPointAndAngle.0
            }
                        
            let mid1 = currentUpKeyB.mid(to: nextUpKeyA)
            let upMid = center2 + (mid1 - center2).normalized.multiplyBy(value: radius2)
            upKeyPointB = upMid
            let mid2 = currentDownKeyB.mid(to: nextDownKeyA)
            let downMid = center2 + (mid2 - center2).normalized.multiplyBy(value: radius2)
            downKeyPointB = downMid
            
            //转角过大处理
            let angle = MathUtils.getAngleOfTwoVectorsWithPoints(center1, center2, center2, center3)
            var handleBigCorner = false
            var tempClockWise = MathUtils.RotatedDirection.collineation
            //转角在20-120之间，只需要处理一侧
            if angle >= 0.3490658504 && angle <= .pi*2/3 {
                handleBigCorner = true
                let clockWise = MathUtils.getClockWise(pointA: center1, pointB: center2, pointC: center3)
                tempClockWise = clockWise
                //系统逆时针，实际的顺时针
                if clockWise == .antiClockwise {
                    downKeyPointB = currentDownKeyB
                    lastDownKeyPointA = nextDownKeyA
                } else if clockWise == .clockwise {
                    upKeyPointB = currentUpKeyB
                    lastUpKeyPointA = nextUpKeyA
                }
            } else if angle >= 0 && angle < 0.3490658504 {
                handleBigCorner = true
                let clockWise = MathUtils.getClockWise(pointA: center1, pointB: center2, pointC: center3)
                tempClockWise = clockWise
                //系统逆时针，实际的顺时针
                if clockWise != .collineation {
                    upKeyPointB = currentUpKeyB
                    downKeyPointB = currentDownKeyB
                    lastUpKeyPointA = nextUpKeyA
                    lastDownKeyPointA = nextDownKeyA
                }
            }
            
            /*该算法的精髓在于，每两个关键点的距离会随着绘制的走势变化。
             比如圆两两不相交，那么关键点之间的距离肯定较大,handleLength也会随之增大；
             如果圆两两相交，那么关键点之间的距离肯定较小,handleLength也会随之减小。
             需要注意的是：处理转角较大的情况。handleLength在内侧需要再次调整。
            */
            let upKeyPointsDistance = upKeyPointA.distanceTo(point: upKeyPointB)
            let downKeyPointsDistance = downKeyPointA.distanceTo(point: downKeyPointB)
            
            
            upHandleLenA = ratio*upKeyPointsDistance
            upHandleLenB = ratio*upKeyPointsDistance
            downHandleLenA = ratio*downKeyPointsDistance
            downHandleLenB = ratio*downKeyPointsDistance
            
            //间隔长转角大处会比较鼓的情况处理
            if handleBigCorner && d > radius1 + radius2 {
                if tempClockWise == .antiClockwise {
                    //处理上
                    upHandleLenB = radius2
                } else if tempClockWise == .clockwise {
                    //处理下
                    downHandleLenB = radius2
                }
            }
            
            upControlB = upKeyPointB - (upKeyPointB - center2).normalized.verticalVector.multiplyBy(value: upHandleLenB)
            downControlB = downKeyPointB + (downKeyPointB - center2).normalized.verticalVector.multiplyBy(value: downHandleLenB)
            
            upControlA = upKeyPointA + (upKeyPointA - center1).normalized.verticalVector.multiplyBy(value: upHandleLenA)
            downControlA = downKeyPointA - (downKeyPointA - center1).normalized.verticalVector.multiplyBy(value: downHandleLenA)
            
            //转角大圆距离很近时导致的有缺角情况（第一个、第二个圆距离远，第二、第三个圆相交但是特别近的情况）
            if handleBigCorner {
                if tempClockWise == .antiClockwise {
                    let angle = MathUtils.getAngleOfTwoVectorsWithPoints(upControlA, upControlB, upControlB, upKeyPointB)
                    if angle <= .pi/3 {
                        upKeyPointB = currentUpKeyB
                        let distance = upKeyPointA.distanceTo(point: upKeyPointB)
                        upControlB = upKeyPointB - (upKeyPointB - center2).normalized.verticalVector.multiplyBy(value: distance*ratio)
                        lastUpKeyPointA = nextUpKeyA
                    }
                } else if tempClockWise == .clockwise {
                    let angle = MathUtils.getAngleOfTwoVectorsWithPoints(downControlA, downControlB, downControlB, downKeyPointB)
                    if angle <= .pi/3 {
                        downKeyPointB = currentDownKeyB
                        let distance = downKeyPointA.distanceTo(point: downKeyPointB)
                        downControlB = downKeyPointB + (downKeyPointB - center2).normalized.verticalVector.multiplyBy(value: distance*ratio)
                        lastDownKeyPointA = nextDownKeyA
                    }
                }
            }
                            
        }
        
        //如果是开始,在上面nextPoint处理
        //如果是结束
        if renderContext?.isEnd == true {
            if d <= abs(radius1 - radius2) {
                //圆包含直接返回空路径
//                print("end 出现圆包含")
                let path = ZDPathUtils.generateCircle(center: center2, radius: radius2)
                return (path,renderContext)
            }
            
            upKeyPointA = cachedKeyPoint?.lastUpKeyPointA ?? upKeyPointA
            downKeyPointA = cachedKeyPoint?.lastDownKeyPointA ?? downKeyPointA
            
            upControlA = upKeyPointA + (upKeyPointA - center1).normalized.verticalVector.multiplyBy(value: radius1)
            downControlA = downKeyPointA - (downKeyPointA - center1).normalized.verticalVector.multiplyBy(value: radius1)
            
            //upKeyPointB和downKeyPointB在上面已经有了初始值
            if let upKeyB = MathUtils.getPointOnCircleWithOtherPoint(center: center2, radius: radius2, outsidePoint: upControlA, isAntiClockwise: false) {
                //上控制点作与下个圆的上切点
                upKeyPointB = upKeyB
            } else if let u1 = MathUtils.getFourContactPointsOfTwoCircle(centerA: center1, centerB: center2, radiusA: radius1, radiusB: radius2) {
                //否则使用公切点
                upKeyPointB = u1.tangencyBEndPointAndAngle.0
            }
            
            if let downKeyB = MathUtils.getPointOnCircleWithOtherPoint(center: center2, radius: radius2, outsidePoint: downControlA, isAntiClockwise: true) {
                //下控制点作与下个圆的下切点
                downKeyPointB = downKeyB
            } else if let u1 = MathUtils.getFourContactPointsOfTwoCircle(centerA: center1, centerB: center2, radiusA: radius1, radiusB: radius2) {
                //否则使用公切点
                downKeyPointB = u1.tangencyBStartPointAndAngle.0
            }
            
            //贝塞尔使用相同的控制点,即后面的绘制变成了二阶贝塞尔曲线
            upControlB = upControlA
            downControlB = downControlA
            
            //相交时内侧线处理
            if d < radius1 + radius2 {
                let upKeyPointsDistance = upKeyPointA.distanceTo(point: upKeyPointB)
                let downKeyPointsDistance = downKeyPointA.distanceTo(point: downKeyPointB)
                //比较连线的长度，决定哪侧的点需要修正。如果up在内侧，修正up
                if upKeyPointsDistance < downKeyPointsDistance {
                    upControlA = upKeyPointA + (upKeyPointA - center1).normalized.verticalVector.multiplyBy(value: upKeyPointsDistance * ratio)
                    upControlB = upControlA
                } else if downKeyPointsDistance < upKeyPointsDistance {
                    //如果down在内侧，修正down
                    downControlA = downKeyPointA - (downKeyPointA - center1).normalized.verticalVector.multiplyBy(value: downKeyPointsDistance * ratio)
                    downControlB = downControlA
                }
            }
        }
        
        //绘制路径
        var path = CGMutablePath()
        path.move(to: upKeyPointA)
        if upControlA == upControlB {
            path.addQuadCurve(to: upKeyPointB, control: upControlA)
        } else {
            path.addCurve(to: upKeyPointB, control1: upControlA, control2: upControlB)
        }
        MathUtils.addArcToPathWithTwoPointsOnCircle(center: center2, pointA: upKeyPointB, pointB: downKeyPointB, radius: radius2, isClockWise: false, path: &path)
        if downControlA == downControlB {
            path.addQuadCurve(to: downKeyPointA, control: downControlB)
        } else {
            path.addCurve(to: downKeyPointA, control1: downControlB, control2: downControlA)
        }
        MathUtils.addArcToPathWithTwoPointsOnCircle(center: center1, pointA: downKeyPointA, pointB: upKeyPointA, radius: radius1, isClockWise: false, path: &path)
        path.closeSubpath()
                
        let newContext = renderContext?.withKeyPoint(newKeyPoint: .init(lastUpKeyPointA: lastUpKeyPointA ?? upKeyPointB, lastDownKeyPointA: lastDownKeyPointA ?? downKeyPointB)).withIsStart(isStart: false).withIsEnd(isEnd: false)
        return (path, newContext)
    }
    
}

//MARK: -控制路径粗细变化相关-
extension ForceFittingPathGenerator_v2 {
    
    //调整将要推入的点大小，控制粗细变化
    fileprivate func controlPointUnitRadiusChanging(penddingPoints: [PointUnitRef], confirmPoints: [PointUnitRef]) -> [PointUnitRef] {
        if let lastPoint = confirmPoints.last?.point ?? penddingPoints.first?.point,
           var radiusA = pointRadius(lastPoint) {
            
            //计算改变量
            let deletaCount:CGFloat = params.calligraphyWidthSmooth * 4.0 + 1.0
            let maxRadius = self.maxRadius
            let minRadius = self.minRadius
            let delta: CGFloat = (maxRadius - minRadius) / deletaCount
            
            for p in penddingPoints {
                let radiusB = p.point.radius ?? 1.0
                var radius = radiusB
                if radiusA - radiusB >= delta {
                    //如果后面的点过小，则调大一点
                    radius = radiusA - delta
                } else if radiusB - radiusA >= delta {
                    //如果后面的点过大，则调小一点
                    radius = radiusA + delta
                } else  {
                    radius = radiusB
                }
                
                //最小宽度限制
                radius = max(minRadius, radius)
                
                p.update(point: p.point.withRadius(radius))
                
                //下个循环约束后面的点
                radiusA = radius
            }
        }
        
        return penddingPoints
    }
    
}

extension ForceFittingPathGenerator_v2 {
    
    private func shouldPeddingToCache(ref: PointUnitRef, isEnd: Bool) -> Optional<PointUnitRef> {
        let location = ref.point.location
        guard var kind = pathPolishTool.push(location) else {
            return nil
        }
        
        if isEnd {
            kind = .normal
        }
        guard kind == .normal else {
            return nil
        }
        
        // 添加更新记录
        if let touch = trackedTouch {
            if touch.estimatedPropertiesExpectingUpdates != [] {
                if let estimationUpdateIndex = touch.estimationUpdateIndex {
                    self.outstandingUpdateIndexes[Int(estimationUpdateIndex.intValue)] = ref
                }
            }
        }
        
        return ref
    }
    
    /// 判断缓存的点集合是否产生了需要提交的点集
    /// - Parameters:
    ///   - cachingPoints: 缓存的点集
    ///   - isEnd: 是否是最后的一批
    /// - Returns: (确认的点集,  剩余缓存的点集)
    private func confirmPointInCaching(cachingPoints: [PointUnitRef], confirmedPoints: [PointUnitRef], params: Params, isEnd: Bool) -> ([PointUnitRef], [PointUnitRef]) {
        var cachingPoints = cachingPoints
        
        // 如果缓存点集合本来就是空的，那必然无法有确定的点集合
        guard cachingPoints.isEmpty == false else {
            return ([], cachingPoints)
        }
        
        // 声明属性, confirmedPoints
        var finalConfirmedPoints: [PointUnitRef] = []
        var tempCachingPoints: [PointUnitRef] = []
        
        // 如果是 Pencil 模式，需要等待点信息更新后，才会产生确定的点集合，因此在这里不会继续执行
//        guard isPencil == false else {
//            return ([], cachingPoints)
//        }
//        if confirmedPoints.count + cachingPoints.count == 3,
//           self.confirmPath.isEmpty,
//            self.confirmedPoints.count <= 1 {
//            
//            // 判断是否要丢弃 缓存的第一个点或(第二个)
//            if self.confirmedPoints.isEmpty {
//                // 丢弃缓存的第二个点 (此时缓存的容量是大于2的)
//                self.cachingPoints.remove(at: 1)
//            } else {
//                // 是否要丢弃第一个缓存点 (此时确定的点 有一个)
//                self.cachingPoints.removeFirst()
//            }
//        }
        guard cachingPoints.count > cacheSize else {
            if isEnd {
                return (cachingPoints, [])
            } else {
                return ([], cachingPoints)
            }
        }
        
        // 获得最后一个确定的点
//        guard let _ = confirmedPoints.last else {
//            // 如果没有确定的点, 则将第一个点作为确定的点
//            finalConfirmedPoints.append(cachingPoints.removeFirst())
//            return (finalConfirmedPoints, cachingPoints)
//        }
        
        // 将超过缓存池大小的点，确定下来
        let penddingSize = cachingPoints.count - cacheSize
        finalConfirmedPoints.append(contentsOf: cachingPoints.prefix(penddingSize))
        cachingPoints.removeFirst(penddingSize)
        
        if isEnd {
            // 让最后一个链接落到最后的采样点上, 这里向后增补一个点，使得最后两个点的中点，刚好是最后一个采样点的位置
//            let lastTwoPoints = Array((confirmedPoints.suffix(2) + cachingPoints).suffix(2))
//            var penddingPoint: Optional<PointUnitRef> = .none
//            if lastTwoPoints.count == 2 {
//                let p = lastTwoPoints[0].point
//                let c = lastTwoPoints[1].point
//                let middle = ZDPointUnit.middle(p1: p, p2: c)
//                let offset = CGPoint(x: c.location.x - middle.location.x, y: c.location.y - middle.location.y)
//                // 沿着 pc 方向，middle - c 的距离
//                
//                penddingPoint = PointUnitRef(point: c.offset(offset))
//                
//            }
            finalConfirmedPoints += cachingPoints
//            if let penddingPoint = penddingPoint {
//                finalConfirmedPoints.append(penddingPoint)
//            }
            tempCachingPoints = []
        } else {
            tempCachingPoints = cachingPoints
        }
        
        return (finalConfirmedPoints, tempCachingPoints)
    }
    
    /// 判断缓存的点集合是否产生了需要提交的点集
    /// 这里是用于 Pencil模式的，只要是更新过的，并且超过缓存池容量限制，都可以提交了
    /// - Parameters:
    ///   - cachingPoints: 缓存的点集
    ///   - isEnd: 是否是最后的一批
    /// - Returns: (确认的点集,  剩余缓存的点集)
    private func confirmPointInCaching4Pencil(cachingPoints: [PointUnitRef], confirmedPoints: [PointUnitRef], params: Params, isEnd: Bool) -> ([PointUnitRef], [PointUnitRef]) {
        var finalCachingPoints = cachingPoints
        
        var finalPenddingPoints: [PointUnitRef] = []
        
        while cachingPoints.first?.hasUpdated == true 
                && finalCachingPoints.count > cacheSize {
            finalPenddingPoints.append(finalCachingPoints.removeFirst())
        }
        return (finalPenddingPoints, finalCachingPoints)
    }
    
    struct CacheKeyPoint {
        let lastUpKeyPointA: Optional<CGPoint>
        let lastDownKeyPointA: Optional<CGPoint>
    }
    
    struct RenderContext {
        var keyPoint: Optional<CacheKeyPoint>
        var isStart: Optional<Bool>
        var isEnd: Optional<Bool>
        
        init(keyPoint: Optional<CacheKeyPoint>, isStart: Optional<Bool>, isEnd: Optional<Bool>) {
            self.keyPoint = keyPoint
            self.isStart = isStart
            self.isEnd = isEnd
        }
        
        func withKeyPoint(newKeyPoint: Optional<CacheKeyPoint>) -> RenderContext {
            var context = self
            context.keyPoint = newKeyPoint
            return context
        }
        
        func withIsStart(isStart: Optional<Bool>) -> RenderContext {
            var context = self
            context.isStart = isStart
            return context
        }
        
        func withIsEnd(isEnd: Optional<Bool>) -> RenderContext {
            var context = self
            context.isEnd = isEnd
            return context
        }
    }
    
    /// 提交新增的确定的点集合，并根据这些信息产生新增的确定路径
    /// 这个方法只是用来渲染的，你不可以在这个方法内更新 `self` 的成员变量，只能读取一些只读的成员变量
    /// - Parameters:
    ///   - confirmed: 已经确定的点集合
    ///   - pendding: 新增的确定的点集合
    ///   - remained: 剩余的缓存点集合 (用来预测未来的路径，用来调整当前的路径信息)
    ///   - isEnd: 是否是结束，指的是最后一次提交
    ///   - lastCurves: 上一段渲染的点集合
    /// - Returns: (渲染的路径，)
    private func _renderConfirmedPoints(confirmed: [PointUnitRef],
                                      pendding: [PointUnitRef],
                                      remained: [PointUnitRef],
                                        context: Optional<RenderContext> = .none
    ) -> (Optional<CGPath>, [PointUnitRef], Optional<RenderContext>) {
        
        // 获得最后一个点
        let penddingPoints: [PointUnitRef] = pendding
        guard pendding.isEmpty == false else {
            return (nil, confirmed, nil)
        }
        let isConfirmed = context != nil
        var ctx: Optional<RenderContext> = context
        var isStart: Optional<Bool> = nil
        let shouldEnd = remained.isEmpty
        if isConfirmed {
            isStart = confirmed.count <= 3
            ctx = ctx?.withIsStart(isStart: isStart)
        }
        
        var finalConfirmedPoints: [PointUnitRef] = confirmed
        let finalPenddingPath: CGMutablePath = CGMutablePath()
        
        //此方法中，不调用系统的updateEstimate方法，只有一个点
        // 遍历获取当前的点和下一个点
        
        if confirmed.isEmpty, remained.isEmpty, pendding.count < 3 {
            finalConfirmedPoints.append(contentsOf: pendding)
            
            let tempPath = CGMutablePath()
            if pendding.count == 2 {
                let p = pendding[0].point
                let c = pendding[1].point
                let outline = handleTwoWhenEnd(circleA: p, circleB: c)
                tempPath.addPath(outline)
            } else if pendding.count == 1 {
                let tempPath = CGMutablePath()
                let p = pendding[0].point
                let path = ZDPathUtils.generateCircle(center: p.location, radius: p.radius ?? self.minRadius)
                tempPath.addPath(path)
            }
            
            finalPenddingPath.addPath(tempPath)
            return (finalPenddingPath.copy(), finalConfirmedPoints, nil)
        }
        
        //结束时等于三个点的情况处理
        if shouldEnd, confirmed.count + pendding.count + remained.count == 3 {
            finalConfirmedPoints.append(contentsOf: pendding)
            finalConfirmedPoints.append(contentsOf: remained)
            
            let pp = finalConfirmedPoints[0].point
            let p = finalConfirmedPoints[1].point
            let c = finalConfirmedPoints[2].point
            
            let mid1 = ZDPointUnit.middle(p1: pp, p2: p)
            let mid2 = ZDPointUnit.middle(p1: p, p2: c)
            
            let outline = handleThreeWhenEnd(circleA: mid1, circleB: mid2)
            let tempPath = CGMutablePath()
            tempPath.addPath(outline)
            finalPenddingPath.addPath(tempPath)
            return (finalPenddingPath.copy(), finalConfirmedPoints, nil)
        }
        
        for (idx, pend) in penddingPoints.enumerated() {
            finalConfirmedPoints.append(pend)
            // 这里取最后三个点，用来渲染出一段路径
            let reqCount: Int = 3
            let final3Points = Array(finalConfirmedPoints.suffix(reqCount))
            
            guard final3Points.count == reqCount else { continue }
            let pp = final3Points[0].point
            let p = final3Points[1].point
            let c = final3Points[2].point
            
            var next: Optional<PointUnitRef> = nil
            if idx < penddingPoints.count - 1 {
                next =  penddingPoints[idx + 1]
            } else {
                next = remained.first
            }
            
                        
            let mid1 = ZDPointUnit.middle(p1: pp, p2: p)
//            if finalConfirmedPoints.count == 3 {
//                mid1 = pp.withRadius(mid1.radius ?? self.minRadius)
//            }
            
            let mid2 = ZDPointUnit.middle(p1: p, p2: c)
            var mid3: ZDPointUnit?
            if let next = next {
                let nextPoint = ZDPointUnit.middle(p1: c, p2: next.point)
                mid3 = nextPoint
            }
           
            let tempPath = CGMutablePath()
            // 这里更新上下文状态，如果是结束的最后一段的渲染，需要设置对应的状态为
            if shouldEnd, idx == penddingPoints.count - 1 {
                ctx = ctx?.withIsEnd(isEnd: true)
            }
//            let startT = CFAbsoluteTimeGetCurrent()
            let (outline,newCtx) = connectCircles(circleA: mid1, transition: p, circleB: mid2, nextPoint: mid3, renderContext: ctx)
            ctx = newCtx
//            let endT = CFAbsoluteTimeGetCurrent()
//            print("use time:",(endT - startT)*1000)
            tempPath.addPath(outline)
            // 按照顺序连接
            finalPenddingPath.addPath(tempPath)
        }
        if finalPenddingPath.isEmpty {
            return (nil, finalConfirmedPoints, nil)
        }
        return (finalPenddingPath.copy(), finalConfirmedPoints, ctx)
    }
    
    private func updateCachingPath(confirmPoints: [PointUnitRef], cachedPoints: [PointUnitRef]) -> Optional<CGPath> {
        let lastTwo = Array(confirmPoints.suffix(2))
        let renderContext = RenderContext(keyPoint: nil, isStart: nil, isEnd: nil)
        let (path, _, _) = _renderConfirmedPoints(confirmed: lastTwo, pendding: cachedPoints, remained: [], context: renderContext)
        return path
    }
    
    private func renderConfirmedPath(confirmed: [PointUnitRef],
                                     pendding: [PointUnitRef],
                                     remained: [PointUnitRef],
                                     updateContext: Bool = true
    ) -> (Optional<CGPath>, [PointUnitRef]) {
        if self.renderContext == nil {
            self.renderContext = .init(RenderContext(keyPoint: nil, isStart: nil, isEnd: nil))
        }
        let  (path, final, ctx) = _renderConfirmedPoints(confirmed: confirmed, pendding: pendding, remained: remained, context: self.renderContext)
        if updateContext {
            self.renderContext = ctx
        }
        return (path, final)
    }
    
    
    // 用于处理当产生了可以确定的点后，这里针对用户的配置来做一些调整
    // 此处是: 压力锥度
    // 会调整确定点集合的压力(半径)
    // 不能超过 配置的 锥度范围
    // 如果判定不建议推入，那需要将 `penddingPoints` 还给 `remainPoints`
    private func mappingPeddingPointsInTaper(penddingPoints: [PointUnitRef], confirmPoints: [PointUnitRef], remainPoints: [PointUnitRef], taperState: TaperState, isEnd: Bool) -> ([PointUnitRef], [PointUnitRef]) {
        let finalPenddingPoints = penddingPoints
        let finalRemainPoints = remainPoints
        
        let penddingPointsRaw = penddingPoints.map(\.point)
        let remainPointsRaw = remainPoints.map(\.point)
        // 如果是开头的
        let minRadius = self.minRadius
        let maxRadiusLimit = self.maxRadius
        
        if taperState.taperHeadDone == false,
           let effectHeadCount = params.taper.effectHeadCount,
           effectHeadCount > 0 {
            let confirmPointsRaw = confirmPoints.map(\.point)
            
            let allRangePoints = (confirmPointsRaw + penddingPointsRaw + remainPointsRaw).prefix(effectHeadCount)
            var maxRadius: CGFloat = maxRadiusLimit
            
            assert(effectHeadCount < cacheSize)
            
            if confirmPointsRaw.isEmpty {
                // 在还没有确定首点的时候
                // 优先取得 索引 为 effectHeadCount 的点
                let targetIdx = effectHeadCount - 1
                
                let targetRadius: CGFloat
                // 优先获取对应索引位的值
                if targetIdx < allRangePoints.count - 1,
                   let targetR = allRangePoints[targetIdx].radius {
                    targetRadius = targetR
                } else if let targetR = allRangePoints.last?.radius {
                    targetRadius = targetR
                } else {
                    targetRadius = maxRadius
                }
                taperState.taperHeadMax = targetRadius
            }
            
            if let maxRadiusCached = taperState.taperHeadMax {
                maxRadius = maxRadiusCached
            }
            
            let radiusRange = maxRadius - minRadius
            let radiusStep = radiusRange / CGFloat(effectHeadCount)
            
            var nowStep = minRadius + CGFloat(confirmPoints.count) * radiusStep
            
            for penddingPoint in finalPenddingPoints + remainPoints {
                if taperState.taperHeadDone { break }
                let curR = penddingPoint.point.radius ?? minRadius
                let newR = max(min(curR, minRadius), nowStep)
                let newP = penddingPoint.point.withRadius(newR)
                penddingPoint.update(point: newP)
                nowStep += radiusStep
                // 如果开头的点 超过了 头部锥度处理长度，那么更新一下flag，表示已经处理过开头了
                let over = confirmPoints.count + penddingPoints.count > effectHeadCount
                taperState.taperHeadDone = over
            }
        }
        
        if let effectTailCount = params.taper.effectTailCount,
                  effectTailCount > 0 {
            
            // 确定最后一个确定的点大小是多少, 如果是尾部的锥度
            let allRangePoints = (penddingPointsRaw + remainPointsRaw).suffix(effectTailCount)
            // 获得倒数第 `effectTailCount` 个
            var maxRadius: CGFloat = minRadius
            if let refR = allRangePoints.first?.radius {
                maxRadius = refR
            } else if let firstOfPendding = penddingPointsRaw.first?.radius {
                maxRadius = firstOfPendding
            } else {
                assert(false, "不会到这个地方")
            }
            
            maxRadius = min(maxRadius, maxRadiusLimit)
            assert(allRangePoints.isEmpty == false)
            
            let radiusRange = maxRadius - minRadius
            let radiusStep = radiusRange / CGFloat(effectTailCount)
            var nowStep = minRadius + CGFloat(allRangePoints.count) * radiusStep
            nowStep = min(nowStep, maxRadiusLimit)
            for penddingPoint in (finalPenddingPoints + finalRemainPoints).suffix(effectTailCount) {
                let curR = penddingPoint.point.radius ?? maxRadius
                let newR = min(nowStep, curR)
                let newP = penddingPoint.point.withRadius(newR)
                penddingPoint.update(point: newP)
                nowStep -= radiusStep
                nowStep = min(max(minRadius, nowStep), maxRadiusLimit)
            }
            
        }
        
        return (finalPenddingPoints, finalRemainPoints)
    }
    
}

//MARK: -系统更新压力方法实现-
extension ForceFittingPathGenerator_v2 {
    //实时更新
    @objc func updateEstimateTouches(noti: Notification) {
        guard let touch = noti.object as? UITouch,
              let index = touch.estimationUpdateIndex else {
            return
        }
//        print("touch.estimationUpdateIndex:",index)
        if let ref = outstandingUpdateIndexes[Int(index.intValue)] {
            var strokeSample = ref.point
            //打印对象的内存地址。确定stroke是同一个笔划
            //                print("stroke:",Unmanaged.passUnretained(stroke).toOpaque())
            let expectedUpdates = strokeSample.estimatedPropertiesExpectingUpdates
            if expectedUpdates.contains(.force) {
//                print("radius before change:",strokeSample.force,touch.force, indexInSamples,strokeSample.radius)
                strokeSample.force = touch.force
                strokeSample.radius = generateRadiusWithForce(force: touch.force)
//                print("radius after change:", strokeSample.radius)
//                strokeSample.originForce = touch.force
                //只有当新值不是估计值时，才移除估计标志
                if !touch.estimatedProperties.contains(.force) {
                    strokeSample.estimatedProperties.remove(.force)
                }
            }

            strokeSample.estimatedPropertiesExpectingUpdates = touch.estimatedPropertiesExpectingUpdates
            if touch.estimatedPropertiesExpectingUpdates == [] {
                outstandingUpdateIndexes.removeValue(forKey: Int(index.intValue))
            }
            ref.update(point: strokeSample, update: true)
            
            // 添加到缓存点集合中，判断是否需要产生确定的点集合， 如果需要产生，返回值就是 确定的点 、 剩余的缓存点集合
            var (penddingPoints, remainPoints) = confirmPointInCaching4Pencil(cachingPoints: self.cachingPoints, confirmedPoints: self.confirmedPoints, params: params, isEnd: false)
            
            // 产生了确定的点集合后，将这些点进行一些处理，例如 压力锥度，压力平滑，流线平滑等
            (penddingPoints, remainPoints) = mappingPeddingPointsInTaper(penddingPoints: penddingPoints, confirmPoints: self.confirmedPoints,remainPoints: remainPoints, taperState: self.taperState, isEnd: false)
            
            var (penddingPath, finalConfirmed) = renderConfirmedPath(confirmed: self.confirmedPoints, pendding: penddingPoints, remained: remainPoints)
            
            self.confirmedPoints = finalConfirmed
            self.cachingPoints = remainPoints
            if let penddingPath = penddingPath {
                self.confirmPath.addPath(penddingPath)
            }
            var effectArea: EffectArea = .zero
            effectArea = penddingPath?.boundingBox ?? .zero
            self.updateEstimateBlock?(effectArea)
        }
    }
}
