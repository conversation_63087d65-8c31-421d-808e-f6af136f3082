//
//  Helps.swift
//  
//
//  Created by WeIHa'S on 2021/12/16.
//

import Foundation
import simd
import CoreGraphics

extension CGRect {
    var center: CGPoint {
        return CGPoint(x: self.midX, y: self.midY)
    }
}


extension CGPoint{
    
    /// 初始化一个A->B向量
    static func-(_ B: CGPoint, _ A: CGPoint) -> vector_double2 {
        return vector_double2(A, B)
    }
    
    static func+(_ A: CGPoint, _ AC: vector_double2) -> CGPoint{
        return CGPoint(x: A.x+AC.x, y: A.y+AC.y)
    }
    
    static func+(_ A: CGPoint, _ B: CGPoint) -> CGPoint{
        return CGPoint(x: A.x+B.x, y: A.y+B.y)
    }

    /// 获取线段AB和CD的交点P
    /// - Parameters:
    /// - Returns: 点P如果存在则为P，否则为空
    static func meetPoints(A: CGPoint, B: CGPoint, C: CGPoint, D: CGPoint) -> CGPoint? {
        // A        D
        //   \    /
        //      P
        //   /    \
        // C        B
        let AB = B - A
        let AC = C - A
        let AD = D - A
        
        let ABC = simd_orient(AB , AC)
        let ABD = simd_orient(AB , AD)
        
        //如果AB叉乘AC和AB叉乘AD符号相同说明C、D在AB的同一侧
        if ABC*ABD > 0{
            return nil
        }
        
        let CD = D-C
        let CA = A-C
        let CB = B-C
        
        let CDA = simd_orient(CD , CA)
        let CDB = simd_orient(CD , CB)
        
        if CDA*CDB > 0{
            return nil
        }
        
        // CP:CD = CP:CP+PD = S_ABC : (S_ABC+S_ABD)
        let CP = (ABC/(ABC-ABD))*CD
        
        let P = C+CP
        return P
    }
    
    
    /// P点到线段AB的长度
    /// - Parameters:
    ///   - P: 某个点
    ///   - A: 线段起点
    ///   - B: 线段终点
    /// - Returns: 距离
    func distance(s A: CGPoint, e B: CGPoint) -> Double {
        
        //      P'        P             P"
        //              / |
        //             /  |
        //            /   |
        //           A ----M-----B
        //
        //AM = AP*AB/|AB| * AB/|AB| = AB*AB*AP/|AB|*|AB|
        let P = self
        let AP = P-A
        let AB = B-A
        
        //AP与AB的向量乘积
        let S = AP*AB
        
        if S < 0{
            //P‘在AB左侧
            return AP.fastLength
        }else if S < AB*AB{
            //P在AB之上
            let AM = simd_fast_project(AP, AB)
            //AM是AP在AB上的快速投影
            return (AP-AM).fastLength
        }else{
            //P在AB右侧
            let BP = P-B
            return BP.fastLength
        }
    }
    
}


extension vector_double2{
    
    /// 初始化向量，召唤一个从A指向B的向量 A-->B
    /// - Parameters:
    ///   - A: 点A
    ///   - B: 点B
    init(_ A: CGPoint, _ B: CGPoint){
        self.init(x: B.x-A.x, y: B.y-A.y)
    }
    
    /// 此向量的长度
    var length: Double{
        return simd_length(self)
    }
    
    /// 此向量的长度(损失一定精度)
    var fastLength: Double{
        return simd_fast_length(self)
    }
    
    ///返回向量长度的平方
    var lengthSqrt: Double{
        return simd_length_squared(self)
    }
    
    ///与该向量的垂直向量（在该向量的逆时针角度）
    var verticalVector: vector_double2{
        return vector_double2(-self.y, self.x)
    }
    
    /// 此向量的法向量
    var normalLize: vector_double2{
        return simd_normalize(self)
    }
    
    /// x轴
    static var xAxis: vector_double2 {
        return vector_double2(x: 1, y: 0)
    }
    
    /// y轴
    static var yAxis: vector_double2 {
        return vector_double2(x: 0, y: 1)
    }
    
    /// 此向量的法向量(损失精度)
    var fastNormalLize: vector_double2{
        return simd_fast_normalize(self)
    }
    
    static func*(_ AB: vector_double2, _ AC: vector_double2) -> Double{
        return simd_dot(AB, AC)
    }
    
    static func+(_ AB: vector_double2, _ BC: vector_double2) -> vector_double2{
        return vector_double2(x: AB.x+BC.x, y: AB.y+BC.y)
    }
    
    static func*(_ n: Double, _ AB: vector_double2) -> vector_double2{
        return vector_double2(x: AB.x*n, y: AB.y*n)
    }
    
    
    /// 此向量在另一向量方向上的投影
    /// - Parameter B: B向量
    /// - Returns: 投影向量
    func fastProject(on B: vector_double2) -> vector_double2 {
        simd_fast_project(self, B)
    }
    
    /// 两向量的夹角
    /// - Parameters:
    ///   - AB: 向量AB
    ///   - AC: 向量AC
    /// - Returns: 角度 -π -> π
    static func angle(from AB: vector_double2, to AC: vector_double2) -> Double{
        let dot = simd_dot(AB, AC)
        
        let length = AB.length*AC.length
        
        var cos = dot/(length == 0 ? 1 : length)
        
        let orient = simd_orient(AB, AC)
        
        if cos > 1 {
            cos = 1
        }else if cos < -1{
            cos = -1
        }
        var angle = acos(cos)

        if orient > 0{
            angle = -angle
        }
        return angle
    }
    
    
    static func cos(from AB: vector_double2, to AC: vector_double2) -> Double{
        let dot = simd_dot(AB, AC)
        let length = AB.length*AC.length
        
        var cos = dot/(length == 0 ? 1 : length)
        
        if cos > 1 {
            cos = 1
        }else if cos < -1{
            cos = -1
        }
        return cos
    }
    
    
}
