//
//  File.swift
//  
//
//  Created by WeIHa'S on 2022/6/7.
//

import Foundation
import CoreGraphics
import simd

extension CGRect {
    var center: CGPoint {
        return CGPoint(x: self.midX, y: self.midY)
    }
    
    var opposite: CGPoint {
        return CGPoint(x: self.maxY, y: self.maxY)
    }
    
    init(points: [CGPoint]) {
        //原点
        let origin: CGPoint = points.reduce(into: CGPoint(x: 100000000, y: 100000000)) { partialResult, point in
            partialResult.x = min(partialResult.x, point.x)
            partialResult.y = min(partialResult.y, point.y)
        }
        //对角
        let opposite: CGPoint = points.reduce(into: CGPoint.zero) { partialResult, point in
            partialResult.x = max(partialResult.x, point.x)
            partialResult.y = max(partialResult.y, point.y)
        }
        
        self.init(origin: origin, size: (opposite-origin).size)
    }
    
    
    static func+(_ A: CGRect, _ B : CGRect) -> CGRect{
        let origin = CGPoint(x: min(<PERSON><PERSON>minX, <PERSON>.minX), y: min(A.minY, <PERSON>.minY))
        let opposite = CGPoint(x: max(A.maxX, B.maxX), y: max(A.maxY, B.maxY))
        return CGRect(origin: origin, size: (opposite - origin).size)
    }
    
    static func+=(_ A: inout CGRect, _ B : CGRect){
        let origin = CGPoint(x: min(A.minX, B.minX), y: min(A.minY, B.minY))
        let opposite = CGPoint(x: max(A.maxX, B.maxX), y: max(A.maxY, B.maxY))
        A = CGRect(origin: origin, size: (opposite - origin).size)
    }
    
}

extension CGSize {
    static var one: CGSize {
        return .init(width: 1, height: 1)
    }
    static var two: CGSize {
        return .init(width: 2, height: 2)
    }
    static var three: CGSize {
        return .init(width: 3, height: 3)
    }
    static var four: CGSize {
        return .init(width: 2, height: 4)
    }
}

internal extension CGPoint {
    /// 初始化一个从A指向B的向量
    static func-(_ B: CGPoint, _ A: CGPoint) -> vector_double2 {
        return vector_double2(A, B)
    }
    
    /// A+AB = B
    static func+(_ A: CGPoint, _ AB : vector_double2) -> CGPoint{
        return CGPoint(x: A.x+AB.x, y: A.y+AB.y)
    }
    
    /// A-BA  = B
    static func-(_ A: CGPoint, _ BA: vector_double2) -> CGPoint{
        return CGPoint(x: A.x-BA.x, y: A.y-BA.y)
    }
    
    /// A + B = C
    static func+(_ A: CGPoint, _ B: CGPoint) -> CGPoint{
        return CGPoint(x: A.x+B.x, y: A.y+B.y)
    }
    
    static func*(_ num: Double, _ B: CGPoint) -> CGPoint {
        return CGPoint(x: num*B.x, y: num*B.y)
    }
    
    static func/(_ A: CGPoint, _ n: Double) -> CGPoint{
        return CGPoint(x: A.x/n, y: A.y/n)
    }
    
    static func+=(_ A: inout CGPoint, _ AB: vector_double2) {
        A = A + AB
    }
}

public extension CGPoint {
    
    ///快速向量，从（0，0）到该点
    var vector: vector_double2 {
        vector_double2(x: self.x, y: self.y)
    }
    
    ///是不存在的错误点
    var isNAN: Bool {
        return self.x.isNaN || self.y.isNaN
    }
    
    ///和B的中点
    func mid(to B: CGPoint) -> CGPoint {
        return 0.5*(self + B)
    }
    
    func offset(x: Double = 0, y: Double = 0) -> Self {
        return self + CGPoint(x: x, y: y)
    }
    
    /// 以此点为圆心半径为r的圆的上下左右四个点
    /// - Parameter r: 半径
    /// - Returns: 四个点
    func circlePoints(with r: Double) -> [CGPoint] {
        return [self.offset(x: -r), self.offset(x: r), self.offset(y: -r), self.offset(y: r)]
    }
    
    
    /// 获取线段AB和CD的交点P
    /// - Parameters:
    /// - Returns: 点P如果存在则为P，否则为空
    public static func meetPoints(A: CGPoint, B: CGPoint, C: CGPoint, D: CGPoint) -> CGPoint? {
        guard A != B, C != D else { return nil }
        // A        D
        //   \    /
        //      P
        //   /    \
        // C        B
        let AB = B - A
        let AC = C - A
        let AD = D - A
        
        let ABC = simd_orient(AB , AC)
        let ABD = simd_orient(AB , AD)
        
        //如果AB叉乘AC和AB叉乘AD符号相同说明C、D在AB的同一侧
        if ABC*ABD > 0{
            return nil
        }
        
        let CD = D-C
        let CA = A-C
        let CB = B-C
        
        let CDA = simd_orient(CD , CA)
        let CDB = simd_orient(CD , CB)
        
        if CDA*CDB > 0{
            return nil
        }
        
        // CP:CD = CP:CP+PD = S_ABC : (S_ABC+S_ABD)
        let CP = (ABC/(ABC-ABD))*CD
        
        let P = C+CP
        return P
    }
    
    /// 到线段AB的长度
    /// - Parameters:
    ///   - A: 线段起点
    ///   - B: 线段终点
    /// - Returns: 距离
    func distance(to A: CGPoint, pass B: CGPoint) -> Double {
        
        //      P'        P             P"
        //              / |
        //             /  |
        //            /   |
        //           A ----M-----B
        //
        //AM = AP*AB/|AB| * AB/|AB| = AB*AB*AP/|AB|*|AB|
        let P = self
        let AP = P-A
        let AB = B-A
        
        //AP与AB的向量乘积
        let S = AP*AB
        
        if S < 0{
            //P‘在AB左侧
            return AP.fastLength
        }else if S < AB*AB{
            //P在AB之上
            let AM = simd_fast_project(AP, AB)
            //AM是AP在AB上的快速投影
            return (AP-AM).fastLength
        }else{
            //P在AB右侧
            let BP = P-B
            return BP.fastLength
        }
    }
    

    
    //知两点和对应的向量求交点
    static func intersection(A: CGPoint, dir_A: vector_double2, B: CGPoint, dir_B: vector_double2) -> CGPoint? {
        var P: CGPoint = A.mid(to: B)
        let AB = B-A
        
        let k1 = dir_A.k
        let k2 = dir_B.k
        
        guard k1 != k2 else {
            if simd_orient(dir_A, AB) == 0 {
                return P
            } else {
                return A.mid(to: B)
            }
        }
        
        
        if let k1 = k1, let k2 = k2 {
            let b1 = A.y - k1*A.x
            let b2 = B.y - k2*B.x
            
            let x = (b1-b2)/(k2-k1)
            let y = k1*x + b1
            P = CGPoint(x: x, y: y)
        } else if let k1 = k1 {
            let y = k1*B.x + A.y-k1*A.x
            P = CGPoint(x: B.x, y: y)
        } else if let k2 = k2 {
            let y = k2*A.x + B.y-k2*B.x
            P = CGPoint(x: A.x, y: y)
        } else {
            return nil
        }
        
        let PA = A-P
        let PB = B-P
        
        
        let angle = vector_double2.angle(from: PA, to: PB)

        if abs(angle) > Double.pi/4 {
            return P
        } else {
            return nil
        }
    }
    
    ///获取直线A和直线B的交点，可能不在两条线段上
    static func meetPoint2(A0: CGPoint, A1: CGPoint, B0: CGPoint, B1: CGPoint) -> CGPoint? {
        guard A0 != A1, B0 != B1 else {
            return nil
        }
        
        var P: CGPoint? = nil
        let k1 = (A1-A0).k
        let k2 = (B1-B0).k
        
        //不可平行
        guard k1 != k2 else {
            //如果平行判断是否共线
            if simd_orient(A1-A0, B0-A0) == 0 {
                return A1.mid(to: B0)
            } else {
                return nil
            }
        }
        

        if let k1 = k1, let k2 = k2 {
            let b1 = A0.y - k1*A0.x
            let b2 = B0.y - k2*B0.x
            
            let x = (b1-b2)/(k2-k1)
            let y = k1*x + b1
            P = CGPoint(x: x, y: y)
        } else if let k1 = k1 {
            let x = B0.x
            let y = k1*x + A0.y-k1*A0.x
            P = CGPoint(x: x, y: y)
        } else if let k2 = k2 {
            let x = A0.x
            let y = k2*x + B0.y-k2*B0.x
            P = CGPoint(x: A0.x, y: y)
        } else {
            assert(false)
        }
        
        return P
    }
}
