//
//  Bezier+Canvas.swift
//  ZDDrawingKit
//
//  Created by tree on 2021/12/1.
//

import simd
import Foundation
import BezierKit

public extension UIBezierPath {
    func intersectionWithClosedBezier(closedBezier: UIBezierPath) -> Bool {
        return self.cgPath.intersectionWithClosedPath(path: closedBezier.cgPath)
    }
    
    func intersectionWithClosedBezier(closedPath: CGPath) -> <PERSON><PERSON> {
        //遍历计算，追求精确度
        return self.cgPath.intersectionWithClosedPath(path: closedPath)
    }
}


public extension CGPath {
    func intersectionWithClosedPath(path: CGPath) -> <PERSON><PERSON> {
        // 容错处理，如果有一个为空，则返回false
        if self.isEmpty { return false }
        if path.isEmpty { return false }
        
        //遍历计算，追求精确度
        var isIntersecion = false
        self.applyWithBlock { (pointer) in
            // 已经确定相交后，之后的就可以避免计算了
            if isIntersecion == true { return }
            var keyPoint = CGPoint.zero
            let points = pointer.pointee.points
            switch pointer.pointee.type {
            case .moveToPoint:
                keyPoint = points[0]
                break
            case .addLineToPoint:
                keyPoint = points[0]
                break
            case .addQuadCurveToPoint:
                keyPoint = points[1]
                break
            case .addCurveToPoint:
                keyPoint = points[2]
                break
            case .closeSubpath:
                // 在 iOS 18 beta 的设备上发现此处取值会崩溃。排查后，发现不需要此逻辑
                break
            @unknown default:
                break
            }
            if keyPoint.isIllegality() == false {
                isIntersecion = isIntersecion || path.contains(keyPoint)
            }
        }
        return isIntersecion
    }
    
    func intersectionWithClosePath(_ closedPath: CGPath, accuracy: CGFloat?) -> Bool {
        var isIntersection = false
        var lastKeyPoint: CGPoint? = nil
        let closeFrame = closedPath.boundingBox
        let step: CGFloat = accuracy ?? 5
        self.applyWithBlock({ (pointer) in
            if isIntersection == true { return }
            //获取keypoint
            var keyPoint = CGPoint.zero
            let pathElement = pointer.pointee
            
            var curve: Optional<BezierCurve> = nil
            
            switch pathElement.type {
                
            case .moveToPoint:
                keyPoint = pathElement.points[0]
                break
                
            case .addCurveToPoint:
                assert(lastKeyPoint != nil)
                keyPoint = pathElement.points[2]
                let cPoint1 = pathElement.points[0]
                let cPoint2 = pathElement.points[1]
                if let lastKeyPoint = lastKeyPoint {
                    curve = CubicCurve(p0: lastKeyPoint, p1: cPoint1, p2: cPoint2, p3: keyPoint)
                }
                break
                
            case .addLineToPoint:
                assert(lastKeyPoint != nil)
                keyPoint = pathElement.points[0]
                if let lastKeyPoint = lastKeyPoint {
                    curve = LineSegment.init(p0: lastKeyPoint, p1: keyPoint)
                }
                break
                
            case .addQuadCurveToPoint:
                assert(lastKeyPoint != nil)
                let cPoint = pathElement.points[0]
                keyPoint = pathElement.points[1]
                if let lastKeyPoint = lastKeyPoint {
                    curve = QuadraticCurve.init(p0: lastKeyPoint, p1: cPoint, p2: keyPoint)
                }
                break
            case .closeSubpath:
                break
            @unknown default:
                break
            }
            if let curve = curve, isIntersection == false {
                if curve.boundingBox.cgRect.isIllegality() == false,
                   curve.boundingBox.cgRect.containOrBorder(closeFrame),
                   curve.startingPoint.isIllegality() == false,
                   curve.endingPoint.isIllegality() == false
                {
                    let distance: CGFloat = (curve.startingPoint - curve.endingPoint).length
                    let count: Int = min(max(Int(ceil(distance / step)), 1), 40)
                    // 这里的取值是 t 均匀的，但是并不是距离均匀的!这部分可以优化
                    let steps: [CGFloat] = Array(stride(from: CGFloat(0.0), to: CGFloat(1.0), by: CGFloat.Stride(1 / CGFloat(count))))
                    for t in steps {
                        if isIntersection == false {
                            let point = curve.point(at: t)
                            if closedPath.contains(point) {
                                isIntersection = true
                                break
                            }
                        }
                    }
                }
            }
            //更新lastKeypont
            lastKeyPoint = keyPoint
        })
        return isIntersection
    }
}

public extension CGPath {
    func trimSegmentedPath(closedPath: CGPath, accuracy: CGFloat?) -> (CGPath, CGRect) {
        var lastKeyPoint: CGPoint? = nil
        var affectedRect: CGRect = .zero
        let closedPathBox: CGRect = closedPath.boundingBox
        let newPath = CGMutablePath()
        let fixAccuracy = (accuracy ?? 10.0)
        self.applyWithBlock { (pointer) in
            //获取keypoint
            var keyPoint = CGPoint.zero
            
            var isClean = false
            let pathElement = pointer.pointee
            
            var addPathAction: ((CGMutablePath) -> Swift.Void) = {_ in }
            var curve: Optional<BezierCurve> = nil
            
            switch pathElement.type {
                
            case .moveToPoint:
                keyPoint = pathElement.points[0]
                break
                
            case .addCurveToPoint:
                assert(lastKeyPoint != nil)
                keyPoint = pathElement.points[2]
                let cPoint1 = pathElement.points[0]
                let cPoint2 = pathElement.points[1]
                curve = CubicCurve(p0: lastKeyPoint!, p1: cPoint1, p2: cPoint2, p3: keyPoint)
                addPathAction = { $0.addCurve(to: keyPoint, control1: cPoint1, control2: cPoint2) }
                break
                
            case .addLineToPoint:
                assert(lastKeyPoint != nil)
                keyPoint = pathElement.points[0]
                curve = LineSegment.init(p0: lastKeyPoint!, p1: keyPoint)
                addPathAction = { $0.addLine(to: keyPoint) }
                break
                
            case .addQuadCurveToPoint:
                assert(lastKeyPoint != nil)
                let cPoint = pathElement.points[0]
                keyPoint = pathElement.points[1]
                curve = QuadraticCurve.init(p0: lastKeyPoint!, p1: cPoint, p2: keyPoint)
                addPathAction = { $0.addQuadCurve(to: keyPoint, control: cPoint) }
                break
            case .closeSubpath:
                newPath.closeSubpath()
                break
            @unknown default:
                break
            }
            if let curve = curve, isClean == false {
                let box = rectContainPoints(curve.points)
                // 如果包围盒有重合则采样判断
                if curve.boundingBox.cgRect.isIllegality() == false,
                   box.containOrBorder(closedPathBox),
                   curve.startingPoint.isIllegality() == false,
                   curve.endingPoint.isIllegality() == false
                {
                    let distance: CGFloat = (curve.startingPoint - curve.endingPoint).length
                    let count: Int = min(max(Int(ceil(distance / fixAccuracy)), 1), 40)
                    let step = 1.0 / CGFloat(count)
                    for t in stride(from: CGFloat(0.0), to: CGFloat(1.0 + step), by: step) {
                        if isClean == false {
                            let point = curve.point(at: t)
                            if closedPath.contains(point) {
                                isClean = true
                                break
                            }
                        }
                    }
                }
                if isClean == false, let lastP = lastKeyPoint {
                    newPath.move(to: lastP)
                    addPathAction(newPath)
                } else {
                    newPath.move(to: keyPoint)
                }
            }
            if isClean, let originPoint = lastKeyPoint {
                let subRect = CGRect(x: originPoint.x, y: originPoint.y, width: keyPoint.x - originPoint.x, height: keyPoint.y - originPoint.y)
                //如果是擦除的第一次
                if affectedRect.isEmpty {
                    affectedRect = subRect
                } else {
                    //计算总的擦除面积
                    affectedRect = rectsBoundingBox(rects: [subRect, affectedRect])
                }
                
            }
            //更新lastKeypont
            lastKeyPoint = keyPoint
        }
        return (newPath, affectedRect)
    }
}

public extension CGPoint {
    func distanceTo(point: CGPoint) -> CGFloat {
        let pointA = simd_float2(Float(x),Float(y))
        let pointB = simd_float2(Float(point.x),Float(point.y))
        return CGFloat(simd_distance(pointA, pointB))
    }
    
    static func middle(p1: CGPoint, p2: CGPoint) -> CGPoint {
        let x: CGFloat = (p1.x + p2.x) * 0.5
        let y: CGFloat = (p1.y + p2.y) * 0.5
        return CGPoint(x: x, y: y)
    }
    
    func makeRect(offset: CGFloat) -> CGRect {
        return makeRect(width: offset, height: offset)
    }
    
    func makeRect(width: CGFloat, height: CGFloat) -> CGRect {
        return CGRect(x: self.x - width * 0.5, y: self.y - height * 0.5, width: width, height: height)
    }
    
    static func +(lhs: CGPoint, rhs: CGPoint) -> CGPoint {
        return CGPoint(x: lhs.x + rhs.x, y: lhs.y + rhs.y)
    }
    
    static func -(lhs: CGPoint, rhs: CGPoint) -> CGPoint {
        return CGPoint(x: lhs.x - rhs.x, y: lhs.y - rhs.y)
    }
    
    static func *(lhs: CGPoint, rhs: CGFloat) -> CGPoint {
        return CGPoint.init(x: lhs.x * rhs, y: lhs.y * rhs)
    }
    
    static func /(lhs: CGPoint, rhs: CGFloat) -> CGPoint {
        return CGPoint.init(x: lhs.x / rhs, y: lhs.y / rhs)
    }
}

extension CGRect {
    
    /// 是否和其他的`rect2`相交或包围了`rect2`
    /// - Parameter rect2: rect
    func intersectsOrContain(_ rect2: CGRect) -> Bool {
        return self.intersects(rect2) || self.contains(rect2)
    }
    
    func containOrBorder(_ rect2: CGRect) -> Bool {
        let wrapper = rectsBoundingBox(rects: [self, rect2])
        let isContainOrBorder = !(wrapper.height > rect2.height + self.height)
            && !(wrapper.width > rect2.width + self.width)
        return isContainOrBorder
    }
}
