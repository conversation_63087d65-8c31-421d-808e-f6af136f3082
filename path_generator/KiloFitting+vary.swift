//
// KiloFitting+vary.swift
//  
//
//  Created by WeIHa'S on 2022/2/18.
//

import Foundation
import simd
import CoreGraphics

//MARK: Vary
public extension KiloFitting {
    
    
    
    /// 变换的矩形
    /// - Parameters:
    ///   - points: 矩形的四个关键点
    ///   - CIndex: 选中的点的索引
    ///   - P: 选中的点位移的新的位置
    /// - Returns: 新的矩形
    static func getNewRect(points: [CGPoint], CIndex: Int, P: CGPoint) -> [CGPoint] {
        guard points.indices.contains(CIndex), points.count == 4 else { return points }
        //
        // A        D       D1
        //
        // B        C
        //
        //
        // B1               P
        //
        let A = points[(CIndex+2)%4]
        let B = points[(CIndex+3)%4]
        let C = points[CIndex]
        let D = points[(CIndex+1)%4]
        
        let AB = B-A
        let AD = D-A
        
        let CP = P-C
        
        let BB1 = CP.fastProject(on: AB)
        let DD1 = CP.fastProject(on: AD)
        
        let result = [A,B+BB1,P,D+DD1]
        
        
        return result.indices.map({result[(6-CIndex+$0)%4]})
    }
    
    struct CircleParameter {
        public let keypoints: [CGPoint]
        public let frame: CGRect
        public let slopping: Double
        init() {
            self.keypoints = []
            self.frame = .zero
            self.slopping = 0
        }
        init(keypoints: [CGPoint], frame: CGRect, slopping: Double) {
            self.keypoints = keypoints
            self.frame = frame
            self.slopping = slopping
        }
    }
    
    /// 变换的圆形
    /// - Parameters:
    ///   - points: 圆形的四个关键点
    ///   - CIndex: 选中的点的索引
    ///   - P: 选中的点位移的新的位置
    /// - Returns: 新的圆形
    static func getNewCircle(points: [CGPoint], CIndex: Int, P: CGPoint) -> CircleParameter {
        guard points.indices.contains(CIndex), points.count == 4 else { return .init() }
        
        ///         D1
        /// O          D
        ///
        /// A                        C         P1
        ///                  P
        ///       B
        ///         B1
        let A = points[(CIndex+2)%4]
        let B = points[(CIndex+3)%4]
        let C = points[CIndex]
        let D = points[(CIndex+1)%4]
        
        
        let AP = P-A
        let AC = C-A
        let AD = D-A
        let AB = B-A
        let AP1 = AP.fastProject(on: AC)
        
        let enlargeSize = (AP1.fastLength)/(AC.fastLength) * (AP*AC > 0 ? 1.0: -1.0)
        
        let AD1 = enlargeSize*AD
        let AB1 = enlargeSize*AB
        
        let result = [A,A+AB1,A+AP1,A+AD1]
        let r = 0.5*AP1
        
        let center = A+r
        let radius = r.fastLength
        
        let origin = CGPoint(x: center.x-radius, y: center.y-radius)
        
        let d = AP1.fastLength
        let newKeyPoints = result.indices.map({result[(6-CIndex+$0)%4]})
        
        return CircleParameter(keypoints: newKeyPoints, frame: CGRect(origin: origin, size: CGSize(width: d, height: d)), slopping: 0.0)
    }
    
    
    /// 变换的椭圆
    /// - Parameters:
    ///   - points: 椭圆的四个关键点
    ///   - CIndex: 选中的点的索引
    ///   - P: 选中的点位移的新的位置
    /// - Returns: 新的椭圆
    static func getNewOval(points: [CGPoint], CIndex: Int, P: CGPoint) -> CircleParameter {
        guard points.indices.contains(CIndex), points.count == 4 else { return .init() }
        
        ///              D    D1
        ///
        ///A                                    O                      C      P1
        ///
        ///              B     B1                             P
        ///
        
        
        
        let A = points[(CIndex+2)%4]
        let B = points[(CIndex+3)%4]
        let C = points[CIndex]
        let D = points[(CIndex+1)%4]
        
        let AP = P-A
        let AC = C-A
        
        let AP1 = AP.fastProject(on: AC)
        
        let CP1 = AP1-AC
        
        let DD1 = 0.5*CP1
        let B1 = B+DD1
        let C1 = A+AP1
        let D1 = D+DD1
        
        let result = [A,B1,C1,D1]
        
        let center = A + (0.5*AP1)
        
        let DB = B-D
        
        let width = AP1.fastLength
        let height = DB.fastLength
        
        let origin = CGPoint(x: center.x-width/2, y: center.y-height/2)
        
        let sloping = vector_double2.angle(from: AP1, to: .xAxis)
        
        print("angel:",sloping)
        
        let frame = CGRect(origin: origin, size: CGSize(width: width, height: height))
        
        let newKeyPoints = result.indices.map({result[(6-CIndex+$0)%4]})
        
        
        return CircleParameter(keypoints: newKeyPoints, frame: frame, slopping: sloping)
    }
    
    
    
    
    /// 变换的五角星
    /// - Parameters:
    ///   - point: 点
    ///   - CIndex: 选中的点的索引
    ///   - P: 选中的点位移的新的位置
    /// - Returns: 新的五角星
    static func getNewStar(points: [CGPoint], sharpIndex: Int, P: CGPoint) -> [CGPoint] {
        
        
        ///         C
        ///        /     \
        /// A ------ B   P   D
        /// - Parameters:
        ///   - A: A点
        ///   - B: B点
        /// - Returns: 计算出的C和D点
        func countPoints(A: CGPoint, B: CGPoint, clockwise: Double) -> [CGPoint] {
            let cos72 = 0.30901699437
            let sin72 = 0.95105651629
            let AB = B-A
            let BP = cos72*AB
            let AP = AB+BP
            
            let PC_length = sin72*(AB.fastLength)
            
            let PC =  PC_length * (clockwise*AP.verticalVector.fastNormalLize)
            
            let AC = AP + PC
            
            let AD = AP + BP
            
            return [A+AC, A+AD]
        }
        
        guard points.indices.contains(sharpIndex), points.count == 10, sharpIndex%2 == 0 else { return points }
        
        let A = points[(sharpIndex+4)%10]
        let B = points[(sharpIndex+5)%10]
        let C = points[(sharpIndex+6)%10]
        
        let G = points[sharpIndex]
        
        let BG = G-B
        
        let BP = P-B
        
        let BP1 = BP.fastProject(on: BG)
        
        let scaleProportion = BP1.fastLength/BG.fastLength
        
        let BA = A-B
        let A1 = B+(scaleProportion*BA)
        
        var result: [CGPoint] = [A1,B]
        
        //确定这个五角星是不是顺时针画出来的
        let clockWise: Double = simd_orient(B-A, C-B) > 0 ? 1.0 : -1.0
        
        
        while result.count<9 {
            let A = result[result.count-2]
            let B = result[result.count-1]
            result.append(contentsOf: countPoints(A: A, B: B, clockwise: clockWise))
        }
        
        return result.indices.map({result[(16-sharpIndex+$0)%10]})
    }
    
    
    /// 打印debug点
    static func superPrint(points: [CGPoint]){
        for index in points.indices {
            if index == points.count-1 {
                print(".init(\(points[index].x),\(points[index].y))", terminator: "\n")
            }else {
                print(".init(\(points[index].x),\(points[index].y)),", terminator: " ")
            }
        }
    }
    
}
