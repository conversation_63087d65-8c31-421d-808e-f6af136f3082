//
//  Math+Canvas.swift
//  ZDDrawingKit
//
//  Created by wangmian on 2021/12/1.
//

import Foundation
import UIKit
import simd
import KiloPathKit
import BezierKit

// 数学相关的计算
public func rectsBoundingBox( rects: [CGRect]) -> CGRect{
    let tmpRects = rects.filter({$0 != .zero})
    guard let firstRect = tmpRects.first else { return CGRect.zero}
    
    var minX:CGFloat = firstRect.origin.x
    var minY:CGFloat = firstRect.origin.y
    var maxX:CGFloat = firstRect.maxX
    var maxY:CGFloat = firstRect.maxY
    
    for rect in tmpRects {
        minX = min(rect.origin.x, minX)
        minY = min(rect.origin.y, minY)
        
        maxX = max(rect.maxX, maxX)
        maxY = max(rect.maxY, maxY)
    }
    
    return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)

}

public func rectContainPoints(_ points: [CGPoint]) -> CGRect {
    var minX:CGFloat = points.first?.x ?? 0
    var minY:CGFloat = points.first?.y ?? 0
    var maxX:CGFloat = points.first?.x ?? 0
    var maxY:CGFloat = points.first?.y ?? 0
    
    for point in points {
        minX = min(minX, point.x)
        minY = min(minY, point.y)
        
        maxX = max(maxX, point.x)
        maxY = max(maxY, point.y)
    }
    
    return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
}

public func rectContainPoints(_ points: CGPoint...) -> CGRect {
    var minX:CGFloat = points.first?.x ?? 0
    var minY:CGFloat = points.first?.y ?? 0
    var maxX:CGFloat = points.first?.x ?? 0
    var maxY:CGFloat = points.first?.y ?? 0
    
    for point in points {
        minX = min(minX, point.x)
        minY = min(minY, point.y)
        
        maxX = max(maxX, point.x)
        maxY = max(maxY, point.y)
    }
    
    return CGRect(x: minX, y: minY, width: maxX - minX, height: maxY - minY)
}

public func lineWidthTransformedApplyScale(_ width: CGFloat, t: CGAffineTransform) -> CGFloat {
    //通过transform获取scale，考虑了rotate对scale的影响
    let scaleX = sqrt(pow(t.a, 2) + pow(t.b, 2))
    let scaleY = sqrt(pow(t.c, 2) + pow(t.d, 2))
    let scale = min(scaleX, scaleY)
    return width * scale
}

public func getRotateFromTransform(T: CGAffineTransform) -> CGFloat {
    let rotate = atan2(T.b, T.a)
    return rotate
}

public func getScaleFromTransform(T: CGAffineTransform) -> CGPoint {
    let scaleX = sqrt(pow(T.a, 2) + pow(T.b, 2))
    let scaleY = sqrt(pow(T.c, 2) + pow(T.d, 2))
    let scale = CGPoint(x: scaleX, y: scaleY)
    return scale
}

public func getTranslationFromTransform(T: CGAffineTransform) -> CGPoint {
    let translation = CGPoint(x: T.tx, y: T.ty)
    
    return translation
}

public extension CGPoint {
    func isIllegality() -> Bool {
        return x.isNaN || x.isInfinite || y.isNaN || y.isInfinite
    }
}

public extension CGSize {
    func isIllegality() -> Bool {
        return width.isNaN || width.isInfinite || height.isNaN || height.isInfinite
    }
}

public extension CGRect {
    func isIllegality() -> Bool {
        return origin.isIllegality() || size.isIllegality() || self.isInfinite || self.isNull
    }
    
    func isSquare() -> Bool {
        return width == height
    }
    
    func center() -> CGPoint {
        return CGPoint.init(x: self.midX, y: self.midY)
    }
}

public extension CGSize {
    func sizeThatFits(other: CGSize) -> CGSize {
        let w1 = self.width
        let h1 = self.height
        
        let w2 = other.width
        let h2 = other.height
        
        let scale_w = w2 / w1
        let scale_h = h2 / h1
        
        let fitScale = min(scale_w, scale_h)
        let T = CGAffineTransform.init(scaleX: fitScale, y: fitScale)
        let finalSize = self.applying(T)
        return finalSize
    }
}

extension CGPoint {
    internal static func segmentPoints(source: CGPoint, target: CGPoint, count: Int) -> [CGPoint] {
        let sourcePoint = source
        let targetPoint = target
        
        let offsetX = CGFloat(targetPoint.x - sourcePoint.x)
        let offsetY = CGFloat(targetPoint.y - sourcePoint.y)
        
        var segments: [CGPoint] = []
        
        let stepX = offsetX / CGFloat(count)
        let stepY = offsetY / CGFloat(count)
        segments.append(source)
        for i in 1..<count {
            let x = sourcePoint.x + stepX * CGFloat(i)
            let y = sourcePoint.y + stepY * CGFloat(i)
            let point = CGPoint(x: x, y: y)
            segments.append(point)
        }
        segments.append(target)
        return segments
    }
    
    internal static func samplingPoints(start: CGPoint, end: CGPoint, unit: CGFloat) -> [CGPoint] {
        let distance = sqrt((start.x - end.x) * (start.x - end.x) + (start.y - end.y) * (start.y - end.y))
        let count: Int = max(Int(ceil(distance / unit)), 1)
        return segmentPoints(source: start, target: end, count: count)
    }
}

// 用于描述构建一段闭合曲线的参数集合
///   A----centerAC---C                E----centerEG---G
///   |                          |                 |                          |
///  centerAB     centerCD          centerEF     centerGH
///   |                          |                 |                          |
///   B----centerBD---D                F----centerFH---H
public struct FittingSegmentUnit {
    
    // MARK: Segment1
    public let centerAC: CGPoint
    
    // 前两个输入点的中点
    public let centerAD: CGPoint
    
    public let centerBD: CGPoint
    
    // 前一段圆弧的半径
    public let halfCenterACToCenterBD: CGFloat
    // 逆时针的启始角度
    public let angleCenterADToCenterAC: CGFloat
    // 逆时针的终止角度
    public let angleCenterADToCenterBD: CGFloat
    
    // MARK: Segment2
    
    public let centerEG: CGPoint
    
    // 前两个输入点的中点
    public let centerEH: CGPoint
    
    public let centerFH: CGPoint
    
    // 后一段圆弧的半径
    public let halfCenterEGToCenterFH: CGFloat
    // 逆时针的启始角度
    public let angleCenterEHToCenterFH: CGFloat
    // 逆时针的终止角度
    public let angleCenterEHToCenterEG: CGFloat
    
    // MARK: Anther
    // control point backup
    public let C: CGPoint
    
    public let D: CGPoint
    
    public let E: CGPoint
    
    public let F: CGPoint
    
    public let G: CGPoint
    
    public let H: CGPoint
    
    public let A: CGPoint
    
    public let B: CGPoint
    
    static public func zero() -> FittingSegmentUnit {
        FittingSegmentUnit(centerAC: .zero,
                           centerAD: .zero,
                           centerBD: .zero,
                           halfCenterACToCenterBD: 0,
                           angleCenterADToCenterAC: 0,
                           angleCenterADToCenterBD: 0,
                           centerEG: .zero,
                           centerEH: .zero,
                           centerFH: .zero,
                           halfCenterEGToCenterFH: 0,
                           angleCenterEHToCenterFH: 0,
                           angleCenterEHToCenterEG: 0,
                           C: .zero,
                           D: .zero,
                           E: .zero,
                           F: .zero,
                           G: .zero,
                           H: .zero,
                           A: .zero,
                           B: .zero)
    }
    
    public init(centerAC: CGPoint,
                centerAD: CGPoint,
                centerBD: CGPoint,
                halfCenterACToCenterBD: CGFloat,
                angleCenterADToCenterAC: CGFloat,
                angleCenterADToCenterBD: CGFloat,
                centerEG: CGPoint,
                centerEH: CGPoint,
                centerFH: CGPoint,
                halfCenterEGToCenterFH: CGFloat,
                angleCenterEHToCenterFH: CGFloat,
                angleCenterEHToCenterEG: CGFloat,
                C: CGPoint,
                D: CGPoint,
                E: CGPoint,
                F: CGPoint,
                G: CGPoint,
                H: CGPoint,
                A: CGPoint,
                B: CGPoint) {
        
        self.centerAC = centerAC
        self.centerAD = centerAD
        self.centerBD = centerBD
        self.halfCenterACToCenterBD = halfCenterACToCenterBD
        self.angleCenterADToCenterAC = angleCenterADToCenterAC
        self.angleCenterADToCenterBD = angleCenterADToCenterBD
        self.centerEG = centerEG
        self.centerEH = centerEH
        self.centerFH = centerFH
        self.halfCenterEGToCenterFH = halfCenterEGToCenterFH
        self.angleCenterEHToCenterFH = angleCenterEHToCenterFH
        self.angleCenterEHToCenterEG = angleCenterEHToCenterEG
        self.C = C
        self.D = D
        self.E = E
        self.F = F
        self.G = G
        self.H = H
        self.A = A
        self.B = B
    }
}


extension FittingSegmentUnit: Equatable {
    public static func == (lhs: FittingSegmentUnit, rhs: FittingSegmentUnit) -> Bool {
        var result = true
        result = result && lhs.centerAC == rhs.centerAC
        result = result && lhs.centerAD == rhs.centerAD
        result = result && lhs.centerBD == rhs.centerBD
        result = result && lhs.halfCenterACToCenterBD == rhs.halfCenterACToCenterBD
        result = result && lhs.angleCenterADToCenterAC == rhs.angleCenterADToCenterAC
        result = result && lhs.angleCenterADToCenterBD == rhs.angleCenterADToCenterBD
        result = result && lhs.centerEG == rhs.centerEG
        result = result && lhs.centerEH == rhs.centerEH
        result = result && lhs.centerFH == rhs.centerFH
        result = result && lhs.halfCenterEGToCenterFH == rhs.halfCenterEGToCenterFH
        result = result && lhs.angleCenterEHToCenterFH == rhs.angleCenterEHToCenterFH
        result = result && lhs.angleCenterEHToCenterEG == rhs.angleCenterEHToCenterEG
        result = result && lhs.C == rhs.C
        result = result && lhs.D == rhs.D
        result = result && lhs.E == rhs.E
        result = result && lhs.F == rhs.F
        result = result && lhs.A == rhs.A
        result = result && lhs.B == rhs.B
        result = result && lhs.G == rhs.G
        result = result && lhs.G == rhs.G
        return result
    }
    
}


public struct ZDAntiClockWisePathSegmentUnit {
    
//    // MARK: CircleA
    public let centerA_RadiusA: (CGPoint, CGFloat)
//    
//    public let circleAT1StartPointAndAngle: (CGPoint, CGFloat)
//    
//    public let circleAT2EndPointAndAngle: (CGPoint, CGFloat)
//    // 圆B连接圆A曲线使用的控制点，在一条直线上，则为中点
//    public let controlPointAEndToBStart: CGPoint
//    
//    // MARK: CircelB
    public let centerB_RadiusB: (CGPoint, CGFloat)
//    
//    public let circleBT4StartPointAndAngle: (CGPoint, CGFloat)
//    
//    public let circleBT3EndPointAndAngle: (CGPoint, CGFloat)
//    // 圆B连接圆A曲线使用的控制点，在一条直线上，则为中点
//    public let controlPointBEndToAStart: CGPoint
//    
//    public init(centerA_RadiusA: (CGPoint, CGFloat), circleAT1StartPointAndAngle: (CGPoint, CGFloat), circleAT2EndPointAndAngle: (CGPoint, CGFloat), controlPointAEndToBStart: CGPoint, centerB_RadiusB: (CGPoint, CGFloat), circleBT4StartPointAndAngle: (CGPoint, CGFloat), circleBT3EndPointAndAngle: (CGPoint, CGFloat), controlPointBEndToAStart: CGPoint) {
//        self.centerA_RadiusA = centerA_RadiusA
//        self.circleAT1StartPointAndAngle = circleAT1StartPointAndAngle
//        self.circleAT2EndPointAndAngle = circleAT2EndPointAndAngle
//        self.controlPointAEndToBStart = controlPointAEndToBStart
//        self.centerB_RadiusB = centerB_RadiusB
//        self.circleBT4StartPointAndAngle = circleBT4StartPointAndAngle
//        self.circleBT3EndPointAndAngle = circleBT3EndPointAndAngle
//        self.controlPointBEndToAStart = controlPointBEndToAStart
//    }
    public let upKeyPointA: CGPoint
    public let upKeyPointB: CGPoint
    public let downKeyPointA: CGPoint
    public let downKeyPointB: CGPoint
    
    public let upControlPointA: CGPoint
    public let upControlPointB: CGPoint
    public let downControlPointA: CGPoint
    public let downControlPointB: CGPoint
    
    public var updatedCircleBLocation: CGPoint? = Optional.none
    
    init(centerA_RadiusA: (CGPoint, CGFloat), centerB_RadiusB: (CGPoint, CGFloat), upKeyPointA: CGPoint, upKeyPointB: CGPoint, downKeyPointA: CGPoint, downKeyPointB: CGPoint, upControlPointA: CGPoint, upControlPointB: CGPoint, downControlPointA: CGPoint, downControlPointB: CGPoint) {
        self.centerA_RadiusA = centerA_RadiusA
        self.centerB_RadiusB = centerB_RadiusB
        self.upKeyPointA = upKeyPointA
        self.upKeyPointB = upKeyPointB
        self.downKeyPointA = downKeyPointA
        self.downKeyPointB = downKeyPointB
        self.upControlPointA = upControlPointA
        self.upControlPointB = upControlPointB
        self.downControlPointA = downControlPointA
        self.downControlPointB = downControlPointB
    }
    
    
}


// 用于描述两个圆如何根据输入点自然过渡的要素
// 两个圆，四个切点，两个控制点，四个角度
// 从 `tangencyAStartPointAndAngle` 逆时针连接
public struct CircleTransitionSegmentUnit {
    
    // MARK: CircleA
    public let centerAndRadiusA: (CGPoint, CGFloat)
    
    public let tangencyAStartPointAndAngle: (CGPoint, CGFloat)
    
    public let tangencyAEndPointAndAngle: (CGPoint, CGFloat)
    // 圆A连接圆B曲线使用的控制点，如果为空，说明在一条直线上，直接连接即可
    public let controlPointAEndToBStart: CGPoint?
    
    // MARK: CircelB
    public let centerAndRadiusB: (CGPoint, CGFloat)
    
    public let tangencyBStartPointAndAngle: (CGPoint, CGFloat)
    
    public let tangencyBEndPointAndAngle: (CGPoint, CGFloat)
    // 圆B连接圆A曲线使用的控制点，如果为空，说明在一条直线上，直接连接即可
    public let controlPointBEndToAStart: CGPoint?
    
    public init(
        centerAndRadiusA: (CGPoint, CGFloat),
        tangencyAStartPointAndAngle: (CGPoint, CGFloat),
        tangencyAEndPointAndAngle: (CGPoint, CGFloat),
        controlPointAEndToBStart: CGPoint?,
        
        centerAndRadiusB: (CGPoint, CGFloat),
        tangencyBStartPointAndAngle: (CGPoint, CGFloat),
        tangencyBEndPointAndAngle: (CGPoint, CGFloat),
        controlPointBEndToAStart: CGPoint?
    ) {
        self.centerAndRadiusA = centerAndRadiusA
        self.tangencyAStartPointAndAngle = tangencyAStartPointAndAngle
        self.tangencyAEndPointAndAngle = tangencyAEndPointAndAngle
        self.controlPointAEndToBStart = controlPointAEndToBStart
        self.centerAndRadiusB = centerAndRadiusB
        self.tangencyBStartPointAndAngle = tangencyBStartPointAndAngle
        self.tangencyBEndPointAndAngle = tangencyBEndPointAndAngle
        self.controlPointBEndToAStart = controlPointBEndToAStart
    }
    
    public func disbleControlPointBEndToAStart() -> CircleTransitionSegmentUnit {
        return CircleTransitionSegmentUnit(centerAndRadiusA: centerAndRadiusA,
                                           tangencyAStartPointAndAngle: tangencyAStartPointAndAngle,
                                           tangencyAEndPointAndAngle: tangencyAEndPointAndAngle,
                                           controlPointAEndToBStart: controlPointAEndToBStart,
                                           centerAndRadiusB: centerAndRadiusB,
                                           tangencyBStartPointAndAngle: tangencyBStartPointAndAngle,
                                           tangencyBEndPointAndAngle: tangencyBEndPointAndAngle,
                                           controlPointBEndToAStart: nil)
    }
    
    public func disableControlPointAEndToBStart() -> CircleTransitionSegmentUnit {
        return CircleTransitionSegmentUnit(centerAndRadiusA: centerAndRadiusA,
                                           tangencyAStartPointAndAngle: tangencyAStartPointAndAngle,
                                           tangencyAEndPointAndAngle: tangencyAEndPointAndAngle,
                                           controlPointAEndToBStart: nil,
                                           centerAndRadiusB: centerAndRadiusB,
                                           tangencyBStartPointAndAngle: tangencyBStartPointAndAngle,
                                           tangencyBEndPointAndAngle: tangencyBEndPointAndAngle,
                                           controlPointBEndToAStart: controlPointBEndToAStart)
    }
}

extension CircleTransitionSegmentUnit: Equatable {
    public static func == (lhs: CircleTransitionSegmentUnit, rhs: CircleTransitionSegmentUnit) -> Bool {
        var result = true
        result = result && lhs.centerAndRadiusA == rhs.centerAndRadiusA
        result = result && lhs.tangencyAStartPointAndAngle == rhs.tangencyAStartPointAndAngle
        result = result && lhs.tangencyAEndPointAndAngle == rhs.tangencyAEndPointAndAngle
        result = result && lhs.controlPointAEndToBStart == rhs.controlPointAEndToBStart
        result = result && lhs.centerAndRadiusB == rhs.centerAndRadiusB
        result = result && lhs.tangencyBStartPointAndAngle == rhs.tangencyBStartPointAndAngle
        result = result && lhs.tangencyBEndPointAndAngle == rhs.tangencyBEndPointAndAngle
        result = result && lhs.controlPointBEndToAStart == rhs.controlPointBEndToAStart
        return result
    }
}

public struct MathUtils {
    
    public enum RotatedDirection: Int {
        // 顺时针
        case clockwise = 1
        // 共线
        case collineation = 2
        // 逆时针
        case antiClockwise = 3
    }

    public enum RelationOfTwoCircles: Int {
        // 相交
        case intersection = 1
        // 内含
        case contains = 2
        // 分离
        case separation = 3
    }

    public static func rotationAnge(previous: CGPoint, current: CGPoint) -> CGFloat {
        let vector = CGPoint(x: current.x - previous.x, y: current.y - previous.y)
        var slopeAngle = atan(vector.y / vector.x)
        if previous.x <= current.x && previous.y >= current.y {
            //第一象限
        } else if previous.x > current.x && previous.y >= current.y {
            slopeAngle = -.pi + abs(slopeAngle)
        } else if previous.x >= current.x && previous.y < current.y {
            slopeAngle = -.pi - abs(slopeAngle)
        } else if previous.x < current.x && previous.y <= current.y {
            slopeAngle = -.pi*2 + abs(slopeAngle)
        }
        return slopeAngle
    }
    
    //  获取两个圆的四个外切点以及切点所在的偏移角度（相对于x轴正方向）
    public static func getFourOutsideContactPointsOfTwoCircle(centerA: CGPoint, centerB: CGPoint, radiusA: CGFloat, radiusB:CGFloat) -> CircleTransitionSegmentUnit? {
        let vector = CGPoint(x: centerB.x - centerA.x, y: centerB.y - centerA.y)
        //连心线的斜率
        var slopeAngle = atan(vector.y / vector.x)
//        print("slopeAngle: \(slopeAngle)")
        let distance = centerA.distanceTo(point: centerB)
        let radiusDiff = radiusA - radiusB
        //如果两个圆内切或者包含，返回
//        if distance <= abs(radiusDiff) {
//            return nil
//        }
        if centerA.x <= centerB.x && centerA.y >= centerB.y {
            //第一象限
//            print("第一象限")
        } else if centerA.x > centerB.x && centerA.y >= centerB.y {
            //第二象限
            slopeAngle = -.pi + abs(slopeAngle)
//            print("第2象限")
        } else if centerA.x >= centerB.x && centerA.y < centerB.y {
            //第三象限
            slopeAngle = -.pi - abs(slopeAngle)
//            print("第3象限")
        } else if centerA.x < centerB.x && centerA.y <= centerB.y {
            //第四象限
            slopeAngle = -.pi*2 + abs(slopeAngle)
//            print("第4象限")
        }
        //切点，圆心，连心线的夹角
        var angel1: CGFloat = 0
        //以（center.x + randius，center.y）- (center.x，center.y）向量，计算偏移量
        let vectorA = CGPoint(x:  radiusA, y: 0)
        let vectorB = CGPoint(x:  radiusB, y: 0)
        //实际相对于x轴正方向的偏移角
        var t1AngleDiff: CGFloat = 0
        var t2AngleDiff: CGFloat = 0
        
        angel1 = acos(radiusDiff/distance)
        t1AngleDiff = slopeAngle - angel1
        t2AngleDiff = angel1 - abs(slopeAngle)
        
        let offsetA1 = vectorA.applying(.init(rotationAngle: t1AngleDiff))
        let offsetA2 = vectorA.applying(.init(rotationAngle: t2AngleDiff))
        let offsetB1 = vectorB.applying(.init(rotationAngle: t1AngleDiff))
        let offsetB2 = vectorB.applying(.init(rotationAngle: t2AngleDiff))
        let tangency1 = CGPoint(x: centerA.x + offsetA1.x, y: centerA.y + offsetA1.y)
        let tangency2 = CGPoint(x: centerA.x + offsetA2.x, y: centerA.y + offsetA2.y)
        let tangency3 = CGPoint(x: centerB.x + offsetB1.x, y: centerB.y + offsetB1.y)
        let tangency4 = CGPoint(x: centerB.x + offsetB2.x, y: centerB.y + offsetB2.y)
        let unit = CircleTransitionSegmentUnit(centerAndRadiusA: (centerA, radiusA),
                                               tangencyAStartPointAndAngle: (tangency1, t1AngleDiff),
                                               tangencyAEndPointAndAngle: (tangency2, t2AngleDiff),
                                               controlPointAEndToBStart: nil,
                                               
                                               centerAndRadiusB: (centerB, radiusB),
                                               tangencyBStartPointAndAngle: (tangency4, t2AngleDiff),
                                               tangencyBEndPointAndAngle: (tangency3, t1AngleDiff),
                                               controlPointBEndToAStart: nil)
        return unit
    }
    
    public static func getFourContactPointsOfTwoCircle(centerA: CGPoint, centerB: CGPoint, radiusA: CGFloat, radiusB:CGFloat) -> CircleTransitionSegmentUnit? {
        let distance = centerA.distanceTo(point: centerB)
        //如果两个圆内切或者包含，返回
        if distance <= abs(radiusA - radiusB) {
            return nil
        }
//        print("distance:",distance)
        let vector = CGPoint(x: centerB.x - centerA.x, y: centerB.y - centerA.y)
        //连心线的斜率
        var slopeAngle = atan(vector.y / vector.x)
//        print("slopeAngle: \(slopeAngle)")
        let radiusDiff = radiusA - radiusB
        
        if centerA.x <= centerB.x && centerA.y >= centerB.y {
            //第一象限
//            print("第一象限")
        } else if centerA.x > centerB.x && centerA.y >= centerB.y {
            //第二象限
            slopeAngle = -.pi + abs(slopeAngle)
//            print("第2象限")
        } else if centerA.x >= centerB.x && centerA.y < centerB.y {
            //第三象限
            slopeAngle = -.pi - abs(slopeAngle)
//            print("第3象限")
        } else if centerA.x < centerB.x && centerA.y <= centerB.y {
            //第四象限
            slopeAngle = -.pi*2 + abs(slopeAngle)
//            print("第4象限")
        }
        //切点，圆心，连心线的夹角
        var angel1: CGFloat = 0
        //以（center.x + randius，center.y）- (center.x，center.y）向量，计算偏移量
        let vectorA = CGPoint(x:  radiusA, y: 0)
        let vectorB = CGPoint(x:  radiusB, y: 0)
        //实际相对于x轴正方向的偏移角
        var t1AngleDiff: CGFloat = 0
        var t2AngleDiff: CGFloat = 0
        
        angel1 = acos(radiusDiff/distance)
        t1AngleDiff = slopeAngle - angel1
        t2AngleDiff = angel1 - abs(slopeAngle)
        
        let offsetA1 = vectorA.applying(.init(rotationAngle: t1AngleDiff))
        let offsetA2 = vectorA.applying(.init(rotationAngle: t2AngleDiff))
        let offsetB1 = vectorB.applying(.init(rotationAngle: t1AngleDiff))
        let offsetB2 = vectorB.applying(.init(rotationAngle: t2AngleDiff))
        let tangency1 = CGPoint(x: centerA.x + offsetA1.x, y: centerA.y + offsetA1.y)
        let tangency2 = CGPoint(x: centerA.x + offsetA2.x, y: centerA.y + offsetA2.y)
        let tangency3 = CGPoint(x: centerB.x + offsetB1.x, y: centerB.y + offsetB1.y)
        let tangency4 = CGPoint(x: centerB.x + offsetB2.x, y: centerB.y + offsetB2.y)
        assert(tangency1.isNAN == false,"tangency1 is NAN")
        assert(tangency2.isNAN == false,"tangency2 is NAN")
        assert(tangency3.isNAN == false,"tangency3 is NAN")
        assert(tangency4.isNAN == false,"tangency4 is NAN")
        assert(t1AngleDiff.isNaN == false,"t1AngleDiff is NAN")
        assert(t2AngleDiff.isNaN == false,"t2AngleDiff is NAN")
        let unit = CircleTransitionSegmentUnit(centerAndRadiusA: (centerA, radiusA),
                                               tangencyAStartPointAndAngle: (tangency1, t1AngleDiff),
                                               tangencyAEndPointAndAngle: (tangency2, t2AngleDiff),
                                               controlPointAEndToBStart: nil,
                                               
                                               centerAndRadiusB: (centerB, radiusB),
                                               tangencyBStartPointAndAngle: (tangency4, t2AngleDiff),
                                               tangencyBEndPointAndAngle: (tangency3, t1AngleDiff),
                                               controlPointBEndToAStart: nil)
        return unit
    }
    
    public static func getFourOutsideTagentPointsOfTwoCircle(centerA: CGPoint, centerB: CGPoint, radiusA: CGFloat, radiusB:CGFloat) -> (CGPoint,CGPoint,CGPoint,CGPoint)? {
        let vector = CGPoint(x: centerB.x - centerA.x, y: centerB.y - centerA.y)
        //连心线的斜率
        var slopeAngle = atan(vector.y / vector.x)
//        print("slopeAngle: \(slopeAngle)")
        let distance = centerA.distanceTo(point: centerB)
        let radiusDiff = radiusA - radiusB
        //如果两个圆内切或者包含，返回
//        if distance <= abs(radiusDiff) {
//            return nil
//        }
        if centerA.x <= centerB.x && centerA.y >= centerB.y {
            //第一象限
//            print("第一象限")
        } else if centerA.x > centerB.x && centerA.y >= centerB.y {
            //第二象限
            slopeAngle = -.pi + abs(slopeAngle)
//            print("第2象限")
        } else if centerA.x >= centerB.x && centerA.y < centerB.y {
            //第三象限
            slopeAngle = -.pi - abs(slopeAngle)
//            print("第3象限")
        } else if centerA.x < centerB.x && centerA.y <= centerB.y {
            //第四象限
            slopeAngle = -.pi*2 + abs(slopeAngle)
//            print("第4象限")
        }
        //切点，圆心，连心线的夹角
        var angel1: CGFloat = 0
        //以（center.x + randius，center.y）- (center.x，center.y）向量，计算偏移量
        let vectorA = CGPoint(x:  radiusA, y: 0)
        let vectorB = CGPoint(x:  radiusB, y: 0)
        //实际相对于x轴正方向的偏移角
        var t1AngleDiff: CGFloat = 0
        var t2AngleDiff: CGFloat = 0
        
        angel1 = acos(radiusDiff/distance)
        t1AngleDiff = slopeAngle - angel1
        t2AngleDiff = angel1 - abs(slopeAngle)
        
        let offsetA1 = vectorA.applying(.init(rotationAngle: t1AngleDiff))
        let offsetA2 = vectorA.applying(.init(rotationAngle: t2AngleDiff))
        let offsetB1 = vectorB.applying(.init(rotationAngle: t1AngleDiff))
        let offsetB2 = vectorB.applying(.init(rotationAngle: t2AngleDiff))
        let tangency1 = CGPoint(x: centerA.x + offsetA1.x, y: centerA.y + offsetA1.y)
        let tangency2 = CGPoint(x: centerA.x + offsetA2.x, y: centerA.y + offsetA2.y)
        let tangency3 = CGPoint(x: centerB.x + offsetB1.x, y: centerB.y + offsetB1.y)
        let tangency4 = CGPoint(x: centerB.x + offsetB2.x, y: centerB.y + offsetB2.y)
        return (tangency1,tangency2,tangency3,tangency4)
    }
    
    //获取完美的控制点,过渡点必须合理，最好在两个圆心之间，中点附近
    public static func getPerfectControlPoint(centerA: CGPoint, centerB: CGPoint, transitionPoint:CGPoint, radiusA: CGFloat, radiusB:CGFloat) -> CircleTransitionSegmentUnit? {
        //取两圆心的中点
        let centerMid = CGPoint.middle(p1: centerA, p2: centerB)
        //如果过渡点在连心线上，则不需要控制点，说明线是直的
        let angle = getAngleOfTwoVectorsWithPoints(transitionPoint, centerMid, centerMid, centerB)
        //如果角度接近 0/pi/-pi， 也返回
        let toleranceValue:CGFloat = 0.05
        let inTolerance = abs(angle) < toleranceValue
                          || abs(angle - .pi) < toleranceValue
                          || abs(angle + .pi) < toleranceValue
        //如果返回nil，说明线是直的，不需要控制点，直接相连即可
        if angle == 0 || angle == .pi || angle == -.pi || inTolerance {
//            print("return parameters nil!")
            return nil
        }
        //中点到转折点的向量
        let lengthCM_T = transitionPoint.distanceTo(point: centerMid)
        let angleCA_CM_T = getAngleOfTwoVectorsWithPoints(centerA, centerMid, centerMid, transitionPoint)
        let angleCB_CM_T = getAngleOfTwoVectorsWithPoints(centerB, centerMid, centerMid, transitionPoint)
        // 获取transitionPoint与线（centerA，centerB）之间的垂直向量
        var verticalDistance: CGFloat = 0
        var rotateAngel:CGFloat = .pi/2

        let clockwise = getClockWise(pointA: centerA, pointB: transitionPoint, pointC: centerB)
        if clockwise == .antiClockwise {
            rotateAngel = -.pi/2
        }
        if angleCA_CM_T <= .pi/2 {
            verticalDistance = lengthCM_T * sin(angleCA_CM_T)
        } else if angleCB_CM_T < .pi/2 {
            verticalDistance = lengthCM_T * sin(angleCB_CM_T)
        }
        let vectorCM_C2 = CGPoint(x: centerB.x - centerMid.x, y: centerB.y - centerMid.y)
        let verticalVectorPoint = MathUtils.getRotatedVectorOfPoint(vector: vectorCM_C2, center: centerMid, rotateAngle: rotateAngel, length: verticalDistance)
        let verticalVector = CGPoint(x: verticalVectorPoint.x - centerMid.x, y: verticalVectorPoint.y - centerMid.y)
        
        if let values = getFourOutsideContactPointsOfTwoCircle(centerA: centerA, centerB: centerB, radiusA: radiusA, radiusB: radiusB) {
            
            var (tangency1, tangency2, tangency3, tangency4) = (values.tangencyAStartPointAndAngle.0, values.tangencyAEndPointAndAngle.0, values.tangencyBEndPointAndAngle.0, values.tangencyBStartPointAndAngle.0)
            let (t1AngleDiff, t2AngleDiff) = (values.tangencyAStartPointAndAngle.1, values.tangencyAEndPointAndAngle.1)
            let tangencyMid1 = CGPoint.middle(p1: tangency1, p2: tangency3)
            let tangencyMid2 = CGPoint.middle(p1: tangency2, p2: tangency4)
            //获取一个该向量方向上的修正控制点，过渡点决定了轨迹走势.
//            let slopeCenMidToTran = getActualSlopeAngle(pointA: centerMid, pointB: transitionPoint)
            let control1 = CGPoint(x: tangencyMid1.x + verticalVector.x, y: tangencyMid1.y + verticalVector.y)
            let control2 = CGPoint(x: tangencyMid2.x + verticalVector.x, y: tangencyMid2.y + verticalVector.y)
            //切点要重新求，切点变为过该控制点的线与圆相切的点
            let centerAToCont1 = centerA.distanceTo(point: control1)
            let centerBToCont1 = centerB.distanceTo(point: control1)
            let centerAToCont2 = centerA.distanceTo(point: control2)
            let centerBToCont2 = centerB.distanceTo(point: control2)
            //控制点必须在圆外,保证后面的值在有效区间
            var t1ToT3IsStraight = false
            var t2ToT4IsStraight = false
            
            //判断控制点是否在两个圆心之间的合适位置,控制点理想情况是一个靠近过渡点，一个远离过渡点
            //减少不必要的计算
            if centerAToCont1 < radiusA || centerBToCont1 < radiusB {
                t1ToT3IsStraight = true
            } else {
                let anglec13 = getAngleOfTwoVectorsWithPoints(control1, centerA, centerA, centerB)
                if anglec13 > .pi/2 {
                    t1ToT3IsStraight = true
                } else {
                    let anglec31 = getAngleOfTwoVectorsWithPoints(control1, centerB, centerB, centerA)
                    if anglec31 > .pi/2 {
                        t1ToT3IsStraight = true
                    }
                }
            }
            
            if centerAToCont2 < radiusA || centerBToCont2 < radiusB {
                t2ToT4IsStraight = true
            } else {
                let anglec24 = getAngleOfTwoVectorsWithPoints(control2, centerA, centerA, centerB)
                if anglec24 > .pi/2 {
                    t2ToT4IsStraight = true
                } else {
                    let anglec42 = getAngleOfTwoVectorsWithPoints(control2, centerB, centerB, centerA)
                    if anglec42 > .pi/2 {
                        t2ToT4IsStraight = true
                    }
                }
            }
            
            let vectorA = CGPoint(x:  radiusA, y: 0)
            let vectorB = CGPoint(x:  radiusB, y: 0)
            var newT1Angel: CGFloat = 0
            var newT2Angel: CGFloat = 0
            var newT3Angel: CGFloat = 0
            var newT4Angel: CGFloat = 0
            var offsetA1: CGPoint = .zero
            var offsetA2: CGPoint = .zero
            var offsetA3: CGPoint = .zero
            var offsetA4: CGPoint = .zero
            // 获取曲线的走势
            let cont1ToCenterMid = control1.distanceTo(point: centerMid)
            let cont2ToCenterMid = control2.distanceTo(point: centerMid)
            
            //新切点圆心控制点连线的夹角
            if t1ToT3IsStraight == false {
                let newTangency1AngleOfCont1 = acos(radiusA/centerAToCont1)
                let oldTangency1AngleOfCont1 = getAngleOfTwoVectorsWithPoints(tangency1, centerA, centerA, control1)
                let newTangency3AngleOfCont1 = acos(radiusB/centerBToCont1)
                let oldTangency3AngleOfCont1 = getAngleOfTwoVectorsWithPoints(tangency3, centerB, centerB, control1)
                let angelDiff1 = abs(newTangency1AngleOfCont1 - oldTangency1AngleOfCont1)
                let angelDiff3 = abs(newTangency3AngleOfCont1 - oldTangency3AngleOfCont1)
                if cont1ToCenterMid > cont2ToCenterMid {
                    // 旧切点到新切点的角度
                    newT1Angel = t1AngleDiff - angelDiff1
                    newT3Angel = t1AngleDiff + angelDiff3
                } else {
                    newT1Angel = t1AngleDiff + angelDiff1
                    newT3Angel = t1AngleDiff - angelDiff3
                }
                offsetA1 = vectorA.applying(.init(rotationAngle: newT1Angel))
                tangency1 = CGPoint(x: centerA.x + offsetA1.x, y: centerA.y + offsetA1.y)
                
                offsetA3 = vectorB.applying(.init(rotationAngle: newT3Angel))
                tangency3 = CGPoint(x: centerB.x + offsetA3.x, y: centerB.y + offsetA3.y)
            } else {
                newT1Angel = t1AngleDiff
                newT3Angel = t1AngleDiff
            }
            
            if t2ToT4IsStraight == false {
                let newTangency2AngleOfCont2 = acos(radiusA/centerAToCont2)
                let oldTangency2AngleOfCont2 = getAngleOfTwoVectorsWithPoints(tangency2, centerA, centerA, control2)
                let newTangency4AngleOfCont2 = acos(radiusB/centerBToCont2)
                let oldTangency4AngleOfCont2 = getAngleOfTwoVectorsWithPoints(tangency4, centerB, centerB, control2)
                let angelDiff2 = abs(newTangency2AngleOfCont2 - oldTangency2AngleOfCont2)
                let angelDiff4 = abs(newTangency4AngleOfCont2 - oldTangency4AngleOfCont2)
                if cont1ToCenterMid > cont2ToCenterMid {
                    // 旧切点到新切点的角度
                    newT2Angel = t2AngleDiff - angelDiff2
                    newT4Angel = t2AngleDiff + angelDiff4
                } else {
                    newT2Angel = t2AngleDiff + angelDiff2
                    newT4Angel = t2AngleDiff - angelDiff4
                }
                offsetA2 = vectorA.applying(.init(rotationAngle: newT2Angel))
                tangency2 = CGPoint(x: centerA.x + offsetA2.x, y: centerA.y + offsetA2.y)
                
                offsetA4 = vectorB.applying(.init(rotationAngle: newT4Angel))
                tangency4 = CGPoint(x: centerB.x + offsetA4.x, y: centerB.y + offsetA4.y)
            } else {
                newT2Angel = t2AngleDiff
                newT4Angel = t2AngleDiff
            }
            
            let unit = CircleTransitionSegmentUnit(centerAndRadiusA: (centerA, radiusA),
                                                   tangencyAStartPointAndAngle: (tangency1, newT1Angel),
                                                   tangencyAEndPointAndAngle: (tangency2, newT2Angel),
                                                   controlPointAEndToBStart: control1,
                                                   centerAndRadiusB: (centerB, radiusB),
                                                   tangencyBStartPointAndAngle: (tangency4, newT4Angel),
                                                   tangencyBEndPointAndAngle: (tangency3, newT3Angel),
                                                   controlPointBEndToAStart: control2)
            if t1ToT3IsStraight == true && t2ToT4IsStraight == false {
                return unit.disableControlPointAEndToBStart()
            } else if t1ToT3IsStraight == false && t2ToT4IsStraight == true  {
                return unit.disbleControlPointBEndToAStart()
            } else if t1ToT3IsStraight == true && t2ToT4IsStraight == true {
                return unit.disbleControlPointBEndToAStart().disableControlPointAEndToBStart()
            }
            
            return unit
        }
//        print("return parameters nil!")
        return nil
    }
    
    /// 获取向量之间的夹角
    /// - Parameters:
    ///   - pointA: A
    ///   - pointB: B
    ///   - pointC: B
    ///   - pointD: C
    public static func getAngleOfTwoVectorsWithPoints(_ pointA: CGPoint, _ pointB: CGPoint, _ pointC:CGPoint, _ pointD: CGPoint) -> CGFloat {
        let vector1 = simd_float2(Float(pointA.x - pointB.x), Float(pointA.y - pointB.y))
        let vector2 = simd_float2(Float(pointD.x - pointC.x), Float(pointD.y - pointC.y))
        let vector1Length = simd_fast_length(vector1)
        let vector2Length = simd_fast_length(vector2)
        let multiplication = simd_dot(vector1, vector2)
        var cosAngle = CGFloat(multiplication / (vector1Length * vector2Length))
        
        //保证计算的结果在有效范围内
        if cosAngle < -1 {
            cosAngle = -1
        }
        if cosAngle > 1 {
            cosAngle = 1
        }
        let angle = acos(cosAngle)
        return angle
    }
    
    //在指定向量的方向上增加长度
    public static func getSpecifiedVector(length: CGFloat, vector: CGPoint) -> CGPoint {
        let simdVector = simd_float2(Float(vector.x),Float(vector.y))
        let unitVector = simd_fast_normalize(simdVector)
        let specifiedVector = CGPoint(x: CGFloat(unitVector.x) * length, y: CGFloat(unitVector.y) * length)
        return specifiedVector
    }
    
    //根据圆上已知点、圆心、夹角，求圆上的任意点坐标
    public static func getPointOnCircle(center: CGPoint, knownPoint:CGPoint ,angle: CGFloat) -> CGPoint {
        let vector = CGPoint(x: knownPoint.x - center.x, y: knownPoint.y - center.y)
        let radius = sqrt(pow(vector.x, 2) + pow(vector.y, 2))
        let x = center.x + radius * cos(angle)
        let y = center.y - radius * sin(angle)
        let point = CGPoint(x: x, y: y)
        return point
    }
    
    //根据圆半径、圆心、夹角，求圆上的任意点坐标
    public static func getPointOnCircle(center: CGPoint, radius:CGFloat ,angle: CGFloat) -> CGPoint {
        let x = center.x + radius * cos(angle)
        let y = center.y - radius * sin(angle)
        let point = CGPoint(x: x, y: y)
        return point
    }
    
    //求圆外点与已知圆的一个切点
    public static func getPointOnCircleWithOtherPoint(center: CGPoint, radius:CGFloat ,outsidePoint: CGPoint, isAntiClockwise: Bool) -> CGPoint? {
        let distance = center.distanceTo(point: outsidePoint)
        guard distance > 0 && distance > radius else {return nil}
        let angle = acos(radius/distance)
        
        var rotateAngle = angle
        if isAntiClockwise {
            rotateAngle = -angle
        }
        let vector = (outsidePoint - center).normalized.applying(CGAffineTransform(rotationAngle: rotateAngle)).multiplyBy(value: radius)
        let point = center + vector
        return point
    }
    
    // 获取旋转后的向量,center是传入的向量某一端的点
    public static func getRotatedVectorOfPoint(vector: CGPoint, center: CGPoint, rotateAngle: CGFloat, length: CGFloat) -> CGPoint {
        let module = sqrt(pow(vector.x, 2) + pow(vector.y, 2))
        let unitVector = CGPoint(x: vector.x/module, y: vector.y/module)
        let rotatedOffSet = unitVector.applying(.init(rotationAngle: rotateAngle))
        let rotatedVector = CGPoint(x: center.x + rotatedOffSet.x*length, y: center.y + rotatedOffSet.y*length)
        return rotatedVector
    }
    
    //判断三个给定的顺序点是顺时针还是逆时针
    public static func getClockWise(pointA: CGPoint, pointB: CGPoint, pointC: CGPoint) -> RotatedDirection {
        let sum = (pointC.y-pointB.y)*(pointB.x-pointA.x) - (pointB.y - pointA.y)*(pointC.x-pointB.x)
        if sum > 0 {
            return RotatedDirection.antiClockwise
        } else if sum == 0 {
            return RotatedDirection.collineation
        } else {
            return RotatedDirection.clockwise
        }
    }
    
    static func twoVectorsAtSameDirection() -> Bool {
        return false
    }
    
    //判断两个圆的相对位置关系
    public static func getRelationShipOfTwoCircles(centerA: CGPoint, centerB: CGPoint, radiusA: CGFloat, radiusB: CGFloat) -> MathUtils.RelationOfTwoCircles {
        let distance = centerA.distanceTo(point: centerB)
        if distance >= radiusA + radiusB {
            return .separation
        } else if distance <= abs(radiusA - radiusB) {
            return .contains
        } else {
            return .intersection
        }
    }
    
}


public extension MathUtils {
    
    /// 通过三个输入点，生成一段拟合的、具有粗细变化的线条
    ///   A----centerAC---C                E----centerEG---G
    ///   |                          |                 |                          |
    ///  centerAB     centerCD          centerEF     centerGH
    ///   |                          |                 |                          |
    ///   B----centerBD---D                F----centerFH---H
    /// - Parameters:
    ///   - points: 用户输入的连续的三个点
    ///   - radiusA: 路径首部半径
    ///   - radiusB: 路径尾部半径
    ///   - forceLimit: 压力的区间 （max , min）
    ///   - speedFixedFactor: 速度系数因子
    /// - Returns: 用于描述拟合线段的参数
    static func generateNeededParametersWithThreePoints(
        points: (ZDPointUnit, ZDPointUnit, ZDPointUnit),
        radiusA: CGFloat,
        radiusB: CGFloat,
        forceLimit: (CGFloat, CGFloat)
    ) -> FittingSegmentUnit {
                
        let (ppUserInputPoint, pUserInputPoint, currentUserInputPoint) = points
        
        let prevDelta = CGPoint(x: pUserInputPoint.location.x - ppUserInputPoint.location.x, y: pUserInputPoint.location.y - ppUserInputPoint.location.y)
        let prevAngle = atan2(prevDelta.y, prevDelta.x)
        
        //  计算之前的两个圆的半径
        let halfLenthAToB = radiusA
        let halfLenthCToD = radiusA
        
        //  计算出相对于每个触摸点自己的偏移量
        let halfABOffset = CGPoint(x: 0, y: halfLenthAToB).applying(.init(rotationAngle: prevAngle))
        let halfCDOffset = CGPoint(x: 0, y: halfLenthCToD).applying(.init(rotationAngle: prevAngle))
        //  根据偏移量得出A，B，C，D点
        let A = CGPoint(x: ppUserInputPoint.location.x - halfABOffset.x, y: ppUserInputPoint.location.y - halfABOffset.y)
        let B = CGPoint(x: ppUserInputPoint.location.x + halfABOffset.x, y: ppUserInputPoint.location.y + halfABOffset.y)
        let C = CGPoint(x: pUserInputPoint.location.x - halfCDOffset.x, y: pUserInputPoint.location.y - halfCDOffset.y)
        let D = CGPoint(x: pUserInputPoint.location.x + halfCDOffset.x, y: pUserInputPoint.location.y + halfCDOffset.y)

        let currentDelta = CGPoint(x: currentUserInputPoint.location.x - pUserInputPoint.location.x, y: currentUserInputPoint.location.y - pUserInputPoint.location.y)
        let currentAngle = atan2(currentDelta.y, currentDelta.x)
        
        let halfLenthEToF = radiusB
        let halfLenthGToH = radiusB
        
        let halfEFOffset = CGPoint(x: 0, y: halfLenthEToF).applying(.init(rotationAngle: currentAngle))
        let halfGHOffset = CGPoint(x: 0, y: halfLenthGToH).applying(.init(rotationAngle: currentAngle))
        
        let E = CGPoint(x: pUserInputPoint.location.x - halfEFOffset.x, y: pUserInputPoint.location.y - halfEFOffset.y)
        let F = CGPoint(x: pUserInputPoint.location.x + halfEFOffset.x, y: pUserInputPoint.location.y + halfEFOffset.y)
        let G = CGPoint(x: currentUserInputPoint.location.x - halfGHOffset.x, y: currentUserInputPoint.location.y - halfGHOffset.y)
        let H = CGPoint(x: currentUserInputPoint.location.x + halfGHOffset.x, y: currentUserInputPoint.location.y + halfGHOffset.y)

        let centerPrev = ZDPointUnit.middle(p1: pUserInputPoint, p2: ppUserInputPoint)
        let centerCurrent = ZDPointUnit.middle(p1: pUserInputPoint, p2: currentUserInputPoint)

        let centerAC = CGPoint.middle(p1: A, p2: C)
        let centerBD = CGPoint.middle(p1: B, p2: D)
        let centerEG = CGPoint.middle(p1: E, p2: G)
        let centerFH = CGPoint.middle(p1: F, p2: H)
        
        let halfLenthCenterEGToCenterFH: CGFloat = centerEG.distanceTo(point: centerFH) * 0.5
        let halfLenthCenterACToCenterBD: CGFloat = centerAC.distanceTo(point: centerBD) * 0.5

        let prevAngle1 = prevAngle - .pi/2
        let prevAngle2 = prevAngle - .pi/2 - .pi

        let currentAngle1 = currentAngle - .pi/2
        let currentAngle2 = currentAngle - .pi/2 - .pi
        return FittingSegmentUnit(
            centerAC: centerAC,
            centerAD: centerPrev.location,
            centerBD: centerBD,
            halfCenterACToCenterBD: halfLenthCenterACToCenterBD,
            angleCenterADToCenterAC: prevAngle1,
            angleCenterADToCenterBD: prevAngle2,
            centerEG: centerEG,
            centerEH: centerCurrent.location,
            centerFH: centerFH,
            halfCenterEGToCenterFH: halfLenthCenterEGToCenterFH,
            angleCenterEHToCenterFH: currentAngle1,
            angleCenterEHToCenterEG: currentAngle2,
            C: C,
            D: D,
            E: E,
            F: F,
            G: G,
            H: H,
            A: A,
            B: B)
    }
    
    
    //速度转压力
    static func transformSpeedToForce(_ speed:CGFloat, _ forceMax:CGFloat, _ forceMin:CGFloat, _ canvasScale:CGFloat) -> CGFloat {
        var force: CGFloat = 0
        //scale 应用
        var fixedSpeed = speed * canvasScale
        let maxSpeed: CGFloat = 1500
        let forceMax: CGFloat = max(forceMax, forceMin)
        let forceMin = min(forceMax, forceMin)
        //定义域
        if fixedSpeed > maxSpeed {
            fixedSpeed = maxSpeed
        }
        
        let speedSectionFactor: CGFloat  = 1000
        //压力区间分段参数
        let forceSectionFactor: CGFloat = 0.8
        
        //此范围内的速度，压力变化映射在（0.8,1.2）
        if fixedSpeed >= 0 && fixedSpeed <= speedSectionFactor {
            force = forceSectionFactor + (speedSectionFactor - fixedSpeed)/(speedSectionFactor-0) * (forceMax - forceSectionFactor)
        }
        //此范围内的速度，压力变化映射在（0.6,1.0）
        if fixedSpeed > speedSectionFactor {
            force = forceMin + (maxSpeed - fixedSpeed)/(maxSpeed-speedSectionFactor) * (forceSectionFactor - forceMin)
        }
        
        return force
    }
    
    //新画笔速度转压力.fasterIsThick越快越粗还是越快越细
    static func newBrushTransformSpeedToForce(_ speed:CGFloat, _ forceMax:CGFloat, _ forceMin:CGFloat, _ canvasScale:CGFloat, fasterIsThick: Bool = true) -> CGFloat {
        var force: CGFloat = 0
        //scale 应用
        var fixedSpeed = speed * canvasScale
        let maxSpeed: CGFloat = 1500
        let forceMax: CGFloat = max(forceMax, forceMin)
        let forceMin = min(forceMax, forceMin)
        //定义域
        if fixedSpeed > maxSpeed {
            fixedSpeed = maxSpeed
        }
        
        if fasterIsThick {
            force = forceMin + abs(maxSpeed-fixedSpeed) / maxSpeed
        } else {
            force = forceMin + maxSpeed / fixedSpeed
        }
            
        return force
    }
    
    //根据时间间隔，求两点之间的速度
    static func getSpeedBetweenTwoPointsWithTimeInterval(pointA: CGPoint, pointB: CGPoint, interval: CGFloat) -> CGFloat {
        if interval.isEqual(to: 0) {
            return 0
        } else {
            let distance = pointA.distanceTo(point: pointB)
            let speed = distance / CGFloat(interval)
            return speed
        }
    }
    
}

public extension MathUtils {
    
    /// 通过三个带有压力系数的输入值，构造一段封闭曲线来反应用户绘制的粗细与路径
    /// - Parameters:
    ///   - previousPrevious: 第一个点
    ///   - previous: 第二个点
    ///   - current: 第三个点
    ///   - radiusA: 路径左侧的半径
    ///   - radiusB: 路径右侧的半径
    /// - Returns: 封闭的曲线
    static func generateForceSubPathWithThreePoints(previousPrevious: ZDPointUnit,
                                                    previous: ZDPointUnit,
                                                    current: ZDPointUnit,
                                                    radiusA: CGFloat,
                                                    radiusB: CGFloat) -> CGPath {
        let prevDelta = CGPoint(x: previous.location.x - previousPrevious.location.x, y: previous.location.y - previousPrevious.location.y)
        let prevAngle = atan2(prevDelta.y, prevDelta.x)
        
        let prevAB = radiusA
        let prevCD = radiusA
        
        let prevABOffset = CGPoint(x: 0, y: prevAB).applying(.init(rotationAngle: prevAngle))
        let prevCDOffset = CGPoint(x: 0, y: prevCD).applying(.init(rotationAngle: prevAngle))
        let prevA = CGPoint(x: previousPrevious.location.x - prevABOffset.x, y: previousPrevious.location.y - prevABOffset.y)
        let prevB = CGPoint(x: previousPrevious.location.x + prevABOffset.x, y: previousPrevious.location.y + prevABOffset.y)
        let prevC = CGPoint(x: previous.location.x - prevCDOffset.x, y: previous.location.y - prevCDOffset.y)
        let prevD = CGPoint(x: previous.location.x + prevCDOffset.x, y: previous.location.y + prevCDOffset.y)
        
        let currentDelta = CGPoint(x: current.location.x - previous.location.x, y: current.location.y - previous.location.y)
        let currentAngle = atan2(currentDelta.y, currentDelta.x)
        
        let currentAB = radiusB
        let currentCD = radiusB
        
        let currentABOffset = CGPoint(x: 0, y: currentAB).applying(.init(rotationAngle: currentAngle))
        let currentCDOffset = CGPoint(x: 0, y: currentCD).applying(.init(rotationAngle: currentAngle))
        let currentA = CGPoint(x: previous.location.x - currentABOffset.x, y: previous.location.y - currentABOffset.y)
        let currentB = CGPoint(x: previous.location.x + currentABOffset.x, y: previous.location.y + currentABOffset.y)
        let currentC = CGPoint(x: current.location.x - currentCDOffset.x, y: current.location.y - currentCDOffset.y)
        let currentD = CGPoint(x: current.location.x + currentCDOffset.x, y: current.location.y + currentCDOffset.y)
        
        let centerPrev = ZDPointUnit.middle(p1: previous, p2: previousPrevious)
        let centerCurrent = ZDPointUnit.middle(p1: previous, p2: current)
        
        let centerPrevAC = CGPoint.middle(p1: prevA, p2: prevC)
        let centerPrevBD = CGPoint.middle(p1: prevB, p2: prevD)
        
        let controlC = prevC
        let controlD = prevD
        
        let centerCurrentAC = CGPoint.middle(p1: currentA, p2: currentC)
        let centerCurrentBD = CGPoint.middle(p1: currentB, p2: currentD)
        
        let centerCurrentWidth: CGFloat = centerCurrentAC.distanceTo(point: centerCurrentBD) * 0.5
        let centerPrevWidth: CGFloat = centerPrevAC.distanceTo(point: centerPrevBD) * 0.5
        
        let prevAngle1 = atan2(prevDelta.y, prevDelta.x) - .pi/2
        let prevAngle2 = atan2(prevDelta.y, prevDelta.x) - .pi/2 - .pi
        
        let currentAngle1 = atan2(currentDelta.y, currentDelta.x) - .pi/2
        let currentAngle2 = atan2(currentDelta.y, currentDelta.x) - .pi/2 - .pi
        
        let path = ZDPathUtils.generateAnticlockwisePath(centerPrev.location, centerCurrent.location, centerPrevAC, centerPrevBD, centerCurrentAC, centerCurrentBD, centerPrevWidth, centerCurrentWidth, prevAngle1, prevAngle2, currentAngle1, currentAngle2, controlC, controlD)
        
        return path
    }
    
    static func generateOutLinePoints(previousPrevious: ZDPointUnit,
                                      previous: ZDPointUnit,
                                      current: ZDPointUnit,
                                      next:ZDPointUnit) -> ([CGPoint],[CGPoint])? {
        guard let ppr = previousPrevious.radius,
              let pr = previous.radius,
              let cr = current.radius,
              let nr = next.radius else {return nil}
        
        let cen1 = previousPrevious.location
        let cen2 = previous.location
        let cen3 = current.location
        let cen4 = next.location
        
        guard let unit1 = getFourOutsideTagentPointsOfTwoCircle(centerA: cen1, centerB: cen2, radiusA: ppr, radiusB: pr),
              let unit2 = getFourOutsideTagentPointsOfTwoCircle(centerA: cen2, centerB: cen3, radiusA: pr, radiusB: cr),
              let unit3 = getFourOutsideTagentPointsOfTwoCircle(centerA: cen3, centerB: cen4, radiusA: cr, radiusB: nr)  else {return nil}
        
        var t1 = unit1.0
        var t2 = unit1.1
        var t3 = unit1.2
        var t4 = unit1.3
        
        
        let t5 = unit2.0
        let t6 = unit2.1
        let t7 = unit2.2
        let t8 = unit2.3
        
        var t11 = unit3.2
        var t12 = unit3.3
        
        if cen1.equalTo(cen2) && ppr == pr {
            t1 = t5
            t3 = t5
            t2 = t6
            t4 = t6
        }
        
        if cen3.equalTo(cen4) && cr == nr {
            t11 = t7
//            t9 = t7
            t12 = t8
//            t10 = t8
        }
        
        var o1 = t3
        var o2 = t4
        let v = vector_double2.bisector(from: vector_double2(x: cen2.x - cen1.x, y: cen2.y - cen1.y), to: vector_double2(x: cen3.x - cen2.x, y: cen3.y - cen2.y)).fastNormalLize.point.multiplyBy(value: pr)
        o1 = cen2 - v
        o2 = cen2 + v
        
        var p1 = t7
        var p2 = t8
        let v1 = vector_double2.bisector(from: vector_double2(x: cen3.x - cen2.x, y: cen3.y - cen2.y), to: vector_double2(x: cen4.x - cen3.x, y: cen4.y - cen3.y)).fastNormalLize.point.multiplyBy(value: cr)
        p1 = cen3 - v1
        p2 = cen3 + v1
                
        return ([t1,o1,p1,t11],[t2,o2,p2,t12])
    }

    
    struct OutlineByFourPointUnit {
        public let PPStart: CGPoint
        public let PPEnd: CGPoint
        
        public let PStart: CGPoint
        public let PEnd: CGPoint
        
        public let CStart: CGPoint
        public let CEnd: CGPoint
        
        public let NStart: CGPoint
        public let NEnd: CGPoint
    }
    
    /// 根据四个点生成轮廓
    /// - Parameters:
    ///  - pp: 前前一个点
    ///  - p: 前一个点
    ///  - c: 当前点
    ///  - n: 下一个点
    ///  - Returns: 轮廓
    ///  - Note: 生成轮廓 生成轮廓的时候需要注意，轮廓的生成是有方向的，所以需要注意方向
    static func generateOutLineByFourPoints(pp: ZDPointUnit, p: ZDPointUnit, c: ZDPointUnit, n: ZDPointUnit) -> Optional<OutlineByFourPointUnit> {
        guard let unit1 = MathUtils.generateOutLinePoints(previousPrevious: pp, previous: p, current: c, next: n)
        else { return nil }
        return OutlineByFourPointUnit(
            PPStart: unit1.0[0],
            PPEnd: unit1.1[0],
            PStart: unit1.0[1],
            PEnd: unit1.1[1],
            CStart: unit1.0[2],
            CEnd: unit1.1[2],
            NStart: unit1.0[3],
            NEnd: unit1.1[3]
        )
    }
    

    static func generateClosePathByFourPoints(pp: ZDPointUnit, p: ZDPointUnit, c: ZDPointUnit, n: ZDPointUnit) -> Optional<CGPath> {
        guard let outline = MathUtils.generateOutLineByFourPoints(pp: pp, p: p, c: c, n: n) else {
            return nil
        }
        let outline_start_points = [outline.PPStart, outline.PStart, outline.CStart, outline.NStart]
        let outline_end_points = [outline.PPEnd, outline.PEnd, outline.CEnd, outline.NEnd]
        
        guard let startUnit = caculateCatmullRom(outline_start_points, alpha: 0.5),
              let endUnit = caculateCatmullRom(outline_end_points, alpha: 0.5) else {
            return nil
        }
        let path = CGMutablePath()
        path.move(to: startUnit.p0)
        path.addCurve(to: startUnit.p1, control1: startUnit.ctrl0, control2: startUnit.ctrl1)
        path.addLine(to: endUnit.p1)
        path.addCurve(to: endUnit.p0, control1: endUnit.ctrl1, control2: endUnit.ctrl0)
        path.addLine(to: startUnit.p0)
        path.closeSubpath()
        return path.copy()
    }
    
    //给定圆上的两点生成圆弧。系统顺时针，实际是逆时针；系统逆时针，实际是顺时针
    static func addArcToPathWithTwoPointsOnCircle(center: CGPoint,pointA: CGPoint, pointB: CGPoint,
                                       radius: CGFloat,isClockWise:Bool = true, path:inout CGMutablePath) {
        let vector_Cen_A = CGPoint(x:pointA.x - center.x, y: pointA.y - center.y)
        let vector_Cen_B = CGPoint(x:pointB.x - center.x, y: pointB.y - center.y)
        let azimuthAngleA = atan2(vector_Cen_A.y, vector_Cen_A.x)
        let azimuthAngleB = atan2(vector_Cen_B.y, vector_Cen_B.x)
        
        path.addArc(center: center, radius: radius, startAngle: azimuthAngleA, endAngle: azimuthAngleB, clockwise: isClockWise)
    }
    
    static func addCurvesToPath(curves:[BezierCurve], path: inout CGMutablePath) {
        curves.forEach { curve in
            if curve.points.count == 2 {
                path.addLine(to: curve.points[1])
            } else if curve.points.count == 3 {
                path.addQuadCurve(to: curve.points[2], control: curve.points[1])
            } else if curve.points.count == 4 {
                path.addCurve(to: curve.points[3], control1: curve.points[1], control2: curve.points[2])
            }
        }
    }
    
    //计算两个圆的交点，按照up、down的顺序返回
    static func calculateCircleIntersectionPoints(circle1: ZDPointUnit, circle2: ZDPointUnit) -> [CGPoint]? {
        guard let radius1 = circle1.radius,
              let radius2 = circle2.radius else {return nil}
        
        let d = circle1.location.distanceTo(point: circle2.location)
        
        // 计算交点个数
        if d >= radius1 + radius2 || d < abs(radius1 - radius2) {
            // 两个圆不相交，返回空数组
            return nil
        } else if d == 0 && radius1 == radius2 {
            // 两个圆重合，返回无穷多个交点
            return nil
        } else {
            let a = (pow(radius1, 2) - pow(radius2, 2) + pow(d, 2)) / (2 * d)
            let h = sqrt(pow(radius1, 2) - pow(a, 2))
            
            let x2 = circle1.location.x + a * (circle2.location.x - circle1.location.x) / d
            let y2 = circle1.location.y + a * (circle2.location.y - circle1.location.y) / d
            
            let intersectionX1 = x2 + h * (circle2.location.y - circle1.location.y) / d
            let intersectionY1 = y2 - h * (circle2.location.x - circle1.location.x) / d
            
            let intersectionX2 = x2 - h * (circle2.location.y - circle1.location.y) / d
            let intersectionY2 = y2 + h * (circle2.location.x - circle1.location.x) / d
            
            let p1 = CGPoint(x: intersectionX1, y: intersectionY1)
            let p2 = CGPoint(x: intersectionX2, y: intersectionY2)
            
            let clockWise = getClockWise(pointA: circle1.location, pointB: p1, pointC: circle2.location)
            
            if clockWise == .antiClockwise {
                return [p1,p2]
            }
            
            return [p2,p1]
        }
    }
    
    
}
