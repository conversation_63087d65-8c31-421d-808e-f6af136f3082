//
//  KiloFitting+polish.swift
//  
//
//  Created by WeIHa'S on 2021/12/17.
//

import CoreGraphics
import Foundation
import simd

//MARK: Polish
extension KiloFitting{
    
    ///修正点,把点调整
    public func polish(style: KiloFittingStyle, line: Line, frame: CGRect) -> (style: KiloFittingStyle,line: Line){
        var style = style
        var line = line
        switch style {
        case .triangel:
            line = adjustForAxis(points: line)
        case .rect:
            line = adjustForAxis(points: line)
            line = rectPolish(points: line)
        case .star:
            line = adjustForAxis(points: line)
            line = starPolish(points: line, frame: frame)
        case .circle(_ , _):
            style = circlePolish(points: line, frame: frame)
        case .other:
            line = adjustForAxis(points: line)
        case .openPolygon, .quadraticCurve:
            line = adjustForAxis(points: line)
        case .failure: break
        case .none: break
        }
        
        return (style,line)
    }
    
    ///矩形调整方式
    private func rectPolish(points: Line) -> Line {
        
        
        //            D
        // A          D1
        // |          |
        // |          |
        // |          |
        // |          C
        // B-------- C1
        
        guard points.count == 4 else {return points}
        
        let A = points[0]
        let B = points[1]
        let C = points[2]
        
        var AB = B-A
        let BC = C-B
        
        let k = abs(AB.y/AB.x)
        if k < 1/10{
            AB = AB.fastProject(on: .xAxis)
        }else if k > 10{
            AB = AB.fastProject(on: .yAxis)
        }
        
        //BC在AB垂直的向量方向上的投影
        let BC_S = BC.fastProject(on: AB.verticalVector)
        
        let C1 = B+BC_S
        let BA = -1*AB
        let D1 = C1+BA
        
        return [A,B,C1,D1]
    }
    
    
    /// 调整和X轴或Y轴对齐
    /// - Parameters:
    ///   - points: 传入的点
    ///   - criticalK: 这些线的斜率的临界值，在0-1之间， 比如0.1， 就是指这条线趋近与X轴平行
    /// - Returns: 调整后的点
    private func adjustForAxis(points: Line, criticalK: Double = 0.1) -> Line {
        return points.reduce([]) { partialResult, point in
            guard let last = partialResult.last else { return [point] }
            var newPoint = point
            
            let deltaX = point.x - last.x
            let deltaY = point.y - last.y
            
            let k = abs(deltaY/deltaX)
            
            if deltaX == 0 {
                return partialResult + [point]
            }
            
            if k < criticalK {
                //当这条线趋向与X轴平行的时候
                newPoint.y = last.y
                
            } else if k > 1/criticalK {
                //当这条线趋向与Y轴平行的时候
                newPoint.x = last.x
                
            }
            return partialResult + [newPoint]
        }
    }
    
    
    ///圆形修饰法2
    private func circlePolish(points: Line, frame: CGRect) -> KiloFittingStyle{
        guard points.count > 6 else { return .other }
        let center = CGPoint(x: frame.midX, y: frame.midY)
        let arrows: [vector_double2] = points.map({$0-center})
        
        //椭圆的半长轴
        let longAxis: vector_double2 = arrows.max(by: {$0.fastLength < $1.fastLength}) ?? .zero
        
        //椭圆的半短轴指向的方向
        let shortAxis: vector_double2 = longAxis.verticalVector
        
       
        let shorts = arrows.map({ $0.fastProject(on: shortAxis).fastLength}).sorted(by: >)
        
        //半短轴的长度
        let longLength = longAxis.fastLength
        let shortLength = (shorts[1] + shorts[0])/2
        
        if shortLength/longLength < 1.2 && shortLength/longLength > 0.8{
            let r = (longLength+shortLength)/2
            let frame = CGRect(x: frame.midX-r, y: frame.midY-r, width: 2*r, height: 2*r)
            return KiloFittingStyle.circle(frame: frame, sloping: 0)
        }
        
        let frame = CGRect(x: frame.midX-longLength, y: frame.midY-shortLength, width: 2*longLength, height: 2*shortLength)
        
        
        //计算长轴和X轴的夹角
        let cosValue = vector_double2.cos(from: longAxis, to: .xAxis)
        var angle = acos(abs(cosValue))
        
        if longAxis.y/longAxis.x < 0{
            angle = -angle
        }
        
//        print("polishdAngel: \(angle)")
        
        if abs(angle) < 0.17 {
            //如果长轴和X轴倾角的绝对值小于10度，置为0度
            angle = 0
        } else if abs(angle) > 1.4 {
            //如果长轴和x轴倾角的绝对值大于80度，置为90度
            angle = 1.57
        }
        return KiloFittingStyle.circle(frame: frame, sloping: angle)
    }
    
    
    
    
    /// 星星描述法
    /// - Parameters:
    ///   - points: 点
    ///   - frame: 框架
    private func starPolish(points: Line, frame: CGRect) -> Line {
        //        D
        //
        // A(F) N        B
        //
        //
        //     C      E
        guard points.count == 5 else { return points }
        
        let A = points[0]
        let B = points[1]
        let C = points[2]
        
        let AB = B-A
        let BC = C-B
        
        var result: [CGPoint] = []
        let sin36: Double = 0.***********
        let cos36: Double = 0.***********

        let center = frame.center
        
        //取所有点到中心的和的平均数作为五角星的外接圆的半径
        let radius: Double = points.map({($0-center).fastLength}).reduce(0.0, +)/5.0
        
        //确定这个五角星是不是顺时针画出来的
        let clockWise: Double = simd_orient(AB, BC) < 0 ? 1.0 : -1.0
        
        let AN_length = radius * sin36 / cos36
        
        let AN =  AN_length*(AB.fastNormalLize)
        
        result.append(contentsOf: [A, A+AN])
        
        while result.count<9 {
            let A = result[result.count-2]
            let B = result[result.count-1]
            result.append(contentsOf: countPoints(A: A, B: B, clockwise: clockWise))
        }
        
        return result
    }
    
    ///         C
    ///        /     \
    /// A ------ B   P   D
    /// - Parameters:
    ///   - A: A点
    ///   - B: B点
    /// - Returns: 计算出的C和D点
    private func countPoints(A: CGPoint, B: CGPoint, clockwise: Double) -> [CGPoint] {
        let cos72 = 0.30901699437
        let sin72 = 0.95105651629
        let AB = B-A
        let BP = cos72*AB
        let AP = AB+BP
        
        let PC_length = sin72*(AB.fastLength)
        
        let PC =  PC_length * (clockwise*AP.verticalVector.fastNormalLize)
        
        let AC = AP + PC
        
        let AD = AP + BP
        
        return [A+AC, A+AD]
    }
}
