//
//  KiloFitting.swift
//  Fitting Test
//
//  Created by WeIHa'S on 2021/12/15.
//

import CoreGraphics
import simd

/// 拟合代码
///
/// 使用方法：
/// ```
/// //传入points和frame
/// let fit = KiloFitting(points: points, frame: rect)
/// //识别
/// fit.identify()
///
/// //最后使用fit的style
/// var style = fit.style
/// ```
public struct KiloFitting{
    public typealias Line = [CGPoint]
    private var style: KiloFittingStyle = .none
    internal private(set) var line: Line
    var frame: CGRect
    
    
    /// 首尾距离必须大于五毫米?，否则识别失败
    public var identifyDistance: Double
    
    ///
    ///     A
    ///           D
    ///
    ///B                             C
    ///三角形闭合参数, 此参数的意义在^ABC 和^BCD之和是否小于tri
    ///取值范围在-0.5到0之间，越大识别结果是三角形的概率越高，但是是一个真正的三角形的概率降低
    let tri_parameter: Double = -0.2
    
    
    
    /// 对于四边形识别成矩形的识别参数，此参数代表矩形的这些角度
    /// 取值范围在0-0.5之间，越大识别结果是矩形的概率越高， 但是是真正的矩形的概率降低
    let rect_parameter: Double = 0.15
    
    
    /// 把高于五条边的多边形识别成圆的参数，此参数的物理意义在于相邻的两条边形成的夹角，
    ///取值范围在0-1之间， 越大识别结果是圆的概率越高，但是是真正的圆的概率降低
    let circle_parameter: Double = 0.5
    
    /// 把等于五条边的多边形识别成五角星的参数，此参数的物理意义在于相邻的两条边形成的夹角，
    ///取值范围在-0.5-0之间， 越大识别结果是五角星的概率越高，但是是真正的五角星的概率降低
    let star_parameter: Double = -0.2
    
    
    public init(points: [CGPoint], frame: CGRect, identifyDistance: Double = 50.0){
        self.line = points
        self.frame = frame
        self.identifyDistance = identifyDistance
    }
    
    
    public mutating func appendPoints(points: [CGPoint], frame: CGRect) {
        self.line.append(contentsOf: points)
        self.frame = frame
    }
    
    
    
    
    /// 全自动识别模式，调用此函数，获取结果
    public func identify() -> (style: KiloFittingStyle, line: [CGPoint]){
        var line = self.line
        var style: KiloFittingStyle = .none
        let startTime = CFAbsoluteTimeGetCurrent()
        print("识别开始")
        
        //是否可以识别
        //锐化
        line = sharp(line: line)
        
        // 先判断是否是直线
        let isClosed = self.isClose(line: self.line)
        if isClosed == false,
           let curveResult = voteIsStrightLine(line: line),
           case (KiloFittingStyle.openPolygon(points: _), let points) = curveResult {
            style = .openPolygon(points: points)
            line = points
        }
        // 然后尝试识别曲线
        //闭合, 如果首尾距离小于 `identifyDistance` 触发闭合，否则保持开放
//        var curveLine = sharp(line: line, dgls_length_parameter: 0.12, dgls_angle_parameter: 0.09)
        if isClosed, style == .none {
            var copiedLine = close(line: line)
            // 然后尝试识别图形
            // 图形需要是闭合的
            style = voteIsShape(line: copiedLine)
            
            if style == .other {
                //减小精度再次锐化
                copiedLine = sharp(line: copiedLine, dgls_length_parameter: 0.12, dgls_angle_parameter: 0.09)
                //重新闭合
                copiedLine = close(line: copiedLine, close_relpace_parameter: 0.12)
                //再次分析
                style = voteIsShape(line: copiedLine)
            }
            line = copiedLine
        }
        
        // 尝试识别曲线
        let sharpedLine = sharp(line: self.line)
        if isClosed == false, style == .none,
           let curveResult = voteIsCurve(line: sharpedLine),
           case (KiloFittingStyle.quadraticCurve(let p1, let p2, let ctrl), let points) = curveResult {
            style = .quadraticCurve(p1: p1, p2: p2, ctrl: ctrl)
            line = points
        }
        
        // 以上都没有命中，归类为折线处理
        if style == .none {
            style = .openPolygon(points: line)
        }
        
        //结果修饰
        
        (style,line) = polish(style: style, line: line, frame: self.frame)
        let endTime = CFAbsoluteTimeGetCurrent() - startTime
        print("📐识别成功，是\(style),用时\(endTime)")
        return (style,line)
    }
    
    
    
    
    public func debug() -> DebugData{
        var debugdata = DebugData()
        var line = self.line
        var style: KiloFittingStyle = .none
        let startTime = CFAbsoluteTimeGetCurrent()
        print("识别开始")
        
        
        
        //是否可以识别
        guard self.isClose(line: self.line) else {
            debugdata.style = .failure
            print("识别失败! 首尾距离大于", identifyDistance)
            return debugdata
        }
        
        //锐化
        line = sharp(line: line)
        debugdata.sharp = line
        //闭合
        line = close(line: line)
        debugdata.close = line
        
        //分析
        style = voteIsShape(line: line)
        debugdata.style = style
        
        if style == .other {
            //减小精度再次锐化
            line = sharp(line: line, dgls_length_parameter: 0.12, dgls_angle_parameter: 0.09)
            debugdata.sharp2 = line
            //重新闭合
            line = close(line: line)
            debugdata.close2 = line
            //再次分析
            style = voteIsShape(line: line)
            print(style)
            debugdata.style = style
        }
        
        //结果修饰
        
        (style,line) = polish(style: style, line: line, frame: self.frame)
        debugdata.style = style
        debugdata.polish = line
        let endTime = CFAbsoluteTimeGetCurrent() - startTime
        print("📐识别成功，是\(style),用时\(endTime)")
        return debugdata
    }
    
    public struct DebugData {
        public var sharp: Line = []
        public var close: Line = []
        public var sharp2: Line = []
        public var close2: Line = []
        public var polish: Line = []
        public var style: KiloFittingStyle = .none
    }
}



/// 拟合的图形
public enum KiloFittingStyle: Equatable {
    
    ///还没有识别，请调用Identify
    case none
    
    ///三角形
    case triangel
    
    ///矩形
    case rect
    
    /// 圆或椭圆
    ///- frame:  约束椭圆的框架
    ///- sloping:  椭圆绕核旋转角度，逆时针旋转
    case circle(frame: CGRect, sloping: Double)

    ///一根线(多边形?)
    case other
    
    ///识别失败
    case failure
    
    /// 五角星
    case star
    
    /// 曲线 (三个控制点)
    case quadraticCurve(p1: CGPoint, p2: CGPoint, ctrl: CGPoint)
    /// 开放式多边形（不闭合）
    case openPolygon(points: [CGPoint])
    
    var rawValue: Int{
        switch self {
        case .none:
            return 0
        case .triangel:
            return 3
        case .rect:
            return 4
        case .star:
            return 5
        case .circle:
            return 6
        case .other:
            return 1
        case .quadraticCurve:
            return 7
        case .openPolygon:
            return 8
        case .failure:
            return -1
        }
    }
    
    public static func == (_ lfs: KiloFittingStyle, _ rfs: KiloFittingStyle ) -> Bool{
        return lfs.rawValue == rfs.rawValue
    }
}

