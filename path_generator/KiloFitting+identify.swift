//
//  File.swift
//  
//
//  Created by WeIHa'S on 2022/2/15.
//

import Foundation
import simd
import CoreGraphics

//MARK: Identify
extension KiloFitting {
    
    /// 此函数用以判断这条线首尾是否比identifyDistance大，否则识别失败
    func isClose(line: Line) -> Bool {
        guard let first = line.first, let last = line.last else { return false }
        let distance = (last-first).fastLength
        return distance < identifyDistance
    }
    
    /// 图形锐化
    /// - Parameters:
    ///   - dgls_length_parameter: 道格拉斯普客算法参数, 越小锐化效果越弱
    ///   此参数的意义是决定某点P到线段AB的长度PN的阈值, PN的距离如果小于dgls_length_parameter *  min(frame.width,frame.height), 抛弃P点
    ///   - dgls_angle_parameter: 道格拉斯普客算法参数, 越小锐化效果越强
    public func sharp(line: Line, dgls_length_parameter: Double = 0.04, dgls_angle_parameter: Double = 0.1) -> Line {
        let epsilon = min(frame.height, frame.width)*dgls_length_parameter
        
        /// 道格拉斯普克算法
        /// - Parameter points: 某条线
        /// - Returns: 优化之后的新线
        func douglasPeucker(points: Line) -> Line {
            guard points.count > 2, let start = points.first, let end = points.last else { return points }
            
            var dmax: Double = 0
            var indexOfMax = 0
            
            for index in points.indices {
                let d = points[index].distance(s: start, e: end)
                if d > dmax{
                    indexOfMax = index
                    dmax = d
                }
            }
            
            //            P
            //            ｜
            // A ---------D----------- B
            //如果某点到某直线的最大距离PD大于指定阈值epsilon，且形成的偏角APB（以AP/AB代替）小于指定阈值角，保留关键点P , 否则丢弃AB之间全部点
            if dmax > epsilon && (dmax/(end-start).fastLength) > dgls_angle_parameter{
                //A---D---G
                //以D为分界线，左边左边传ABCD， 右边传DEFG
                let result1 = douglasPeucker(points: Array(points[0..<indexOfMax+1]))
                let result2 = douglasPeucker(points: Array(points[indexOfMax..<points.count]))
                return result1.prefix(result1.count-1) + result2
            } else {
                return [start,end]
            }
        }
        
        return douglasPeucker(points: line)
    }
    
    
    /// 闭合回路
    /// - Parameter close_meet_parameter:  闭合参数，此参数的物理意义是若AB和ZY存在交点P，P在各条线上的比例PA/AB
    /// - Parameter close_relpace_parameter: 闭合参数，此参数的物理意义是若AB和ZY存在交点P，P
    /// 如果起点终点的距离AZ > max(self.frame.width, self.frame.height)*close_factor_parameter, 不可闭合
    public func close(line: Line, close_meet_parameter: Double = 0.25, close_relpace_parameter: Double = 0.04) -> Line {
        guard line.count > 3 else { return line }
        
        var newLine = line
        let replaceAZDistance = max(self.frame.width, self.frame.height)*close_relpace_parameter
        var A = line[0]
        let B = line[1]
        let Y = line[line.endIndex-2]
        var Z = line[line.endIndex-1]

        
        let YZ = Z-Y
        let AB = B-A

        //    Z        A
        //        P
        //
        //B .............Y
        //如果AB和YZ有交点P, 且PA/AB<close_meet_parameter,那么P作为新的A点，如果PZ/ZY<close_meet_parameter，那么P作为新的Z点
        if let P = CGPoint.meetPoints(A: A, B: B, C: Y, D: Z){
            let PA = A-P
            let PZ = Z-P
            if PA.fastLength/AB.fastLength < close_meet_parameter {
                A = P
                newLine[0] = P
            }
            if PZ.fastLength/YZ.fastLength < close_meet_parameter{
                Z = P
                newLine[line.endIndex-1] = P
            }
        }

        //
        //
        //     A .. .. Z
        //    /         \
        //   /           \
        //  /             \
        // B              Y
        //   \           /
        //    C.........X
        //当A和Z比较接近时，线圈尝试闭合
        //A是ABCD...XYZ这条线的起点，Z是终点
        //如果A离BZ过近，说明A可以删除，线圈闭合，起点终点都是Z
        //反之如果Z离AY过近，说明Z可以删除，线圈闭合，起点终点都是A
        //起点A更重要
        
        
        let disZ_AY = Z.distance(s: A, e: Y)
        if disZ_AY < replaceAZDistance, disZ_AY != YZ.fastLength {
            newLine.removeLast()
            Z = Y
        }
        
        let disA_BZ = A.distance(s: B, e: Z)
        if  disA_BZ < replaceAZDistance, disA_BZ != AB.fastLength {
            newLine.removeFirst()
        }
        
        if newLine.count == line.count {
            //如果啥也没改变
            let disZ_AB = Z.distance(s: A, e: B)
            let disA_ZY = A.distance(s: Z, e: Y)
            //如果终点离第一根线很近，删除起点
            if disZ_AB < replaceAZDistance {
                newLine.removeFirst()
                //如果起点离最后一根线很近，删除终点
            } else if disA_ZY < replaceAZDistance {
                newLine.removeLast()
            }
            //否则啥也不干
        }
        return newLine
    }
    
    public func voteIsStrightLine(line: Line) -> (KiloFittingStyle, Line)? {
        let copiedLine = sharp(line: line, dgls_length_parameter: 0.2, dgls_angle_parameter: 0.09)
        if copiedLine.count == 2 {
            
            return (style: KiloFittingStyle.openPolygon(points: copiedLine), line: copiedLine)
        }
        return nil
    }
    
    public func voteIsCurve(line: Line) -> (KiloFittingStyle, Line)? {
        var maxD: Double = 0
        var maxDIndex: Int = 0
        
        guard let start = line.first, let end = line.last else {
            return nil
        }
        var clockwise: Double = 0.0
        var coses: [Double] = []
        var angles: [Double] = []
        var side: [Double] = []
        
        let n = line.count
        // 解析用于分析是否是曲线的参数
        for index in line.indices {
            let A = line[index]
            let B = line[(index+1)%n]
            let C = line[(index+2)%n]
            let d = A.distance(s: start, e: end)
            
            if d > maxD {
                maxDIndex = index
                maxD = d
            }
            
            let AB = B-A
            let BC = C-B
            let BA = A-B
            
            let cosvalue = vector_double2.cos(from: BC, to: AB)
            let angle = vector_double2.angle(from: BA, to: BC)
            let orient = simd_orient(AB, BC)
            // 判断所有的点都在同一侧
            if clockwise != 0, (clockwise > 0) != (orient > 0) {
                return nil
            }
            clockwise = orient
            
            angles.append(angle)
            coses.append(cosvalue)
            side.append(AB.fastLength)
        }
        
        let prevLine = line[0..<maxDIndex]
        let nextLine = line[maxDIndex..<line.count]
        
        let prevSharp = sharp(line: Array(prevLine), dgls_length_parameter: 0.04)
        let nextSharp = sharp(line: Array(nextLine), dgls_length_parameter: 0.04)
        // 过滤角度小于 100度 的
        
        let hasSomeAngleLess90 = angles.filter({ $0 < 1.95 && $0 > -1 }).count >= 3
        // 移除两端的角度
        if (prevSharp.count >= 2 || nextSharp.count >= 2), !hasSomeAngleLess90 {
            return (style: KiloFittingStyle.quadraticCurve(p1: start, p2: end, ctrl: line[maxDIndex]), line: [start, line[maxDIndex], end])
            
        } else if prevLine.count == 1 || nextSharp.count == 1, prevSharp.count + nextSharp.count >= 3, !hasSomeAngleLess90 {
            return (style: KiloFittingStyle.quadraticCurve(p1: start, p2: end, ctrl: line[maxDIndex]), line: [start, line[maxDIndex], end])
        }
        return nil
    }
    
    /// 图像分析
    /// - Returns: 提前判定图像是否闭合
    /// - Parameter line: 一条线（无需闭合）
    public func voteIsShape(line: Line) -> KiloFittingStyle{
        var style: KiloFittingStyle = .none
        var angle: [Double] = []
        var side: [Double] = []
        var clockwise: Double = 0.0
        let n = line.count
        
        for index in line.indices {
            let A = line[index]
            let B = line[(index+1)%n]
            let C = line[(index+2)%n]
            
            let AB = B-A
            let BC = C-B
            
            let cosvalue = vector_double2.cos(from: BC, to: AB)
            let orient = simd_orient(AB, BC)
            
            if clockwise != 0, (clockwise > 0) != (orient > 0) {
                //如果记录的上次叉积和这次的不同号，认定多边形
                if self.isClose(line: self.line) {
                   style = .other
               } else {
                   style = .openPolygon(points: line)
               }
            }
            
            clockwise = orient
            
            angle.append(cosvalue)
            side.append(AB.fastLength)
            
        }
        
        if line.count == 3, angle.count == 3 {
            //三角形
            //如果所有的角中最多只有一个大于或等于直角的角，而且角1+角2 小于tri_parameter
            if angle.filter({$0 >= 0}).count < 2, angle[0] + angle[1] < tri_parameter {
                style = .triangel
            } else {
                style = .other
            }
        }else if line.count == 4, angle.count == 4{
            //四边形
            if angle.filter({$0 > rect_parameter || $0 < -rect_parameter}).isEmpty{
                style = .rect
            } else {
                style = .other
            }
        }else if line.count == 5, angle.count == 5 {
            if angle.filter({$0 > star_parameter}).count == 0 {
                style = .star
            } else {
                style = .other
            }
        }else if line.count > 6, angle.count > 6{
            //圆
            if angle.filter({$0 < -circle_parameter}).count <= 2 {
                style = .circle(frame: self.frame, sloping: 0)
            } else {
                style = .other
            }
        } else {
            style = .other
        }
        return style
    }
}
